(()=>{"use strict";var e,t,a,s,n,r,o,i={69:(e,t,a)=>{a.d(t,{BK:()=>i,eu:()=>o,q5:()=>l});var s=a(4848),n=(a(6540),a(461)),r=a(5284);function o({className:e,...t}){return(0,s.jsx)(n.bL,{"data-slot":"avatar",className:(0,r.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...t})}function i({className:e,...t}){return(0,s.jsx)(n._V,{"data-slot":"avatar-image",className:(0,r.cn)("aspect-square size-full",e),...t})}function l({className:e,...t}){return(0,s.jsx)(n.H4,{"data-slot":"avatar-fallback",className:(0,r.cn)("bg-muted flex size-full items-center justify-center rounded-full",e),...t})}},523:(e,t,a)=>{a.d(t,{V:()=>i});var s=a(4848),n=a(5284),r=a(6948),o=a(7520);const i=()=>{const{config:e}=(0,r.UK)(),t=e?.persona||"default",a=o.z[t]||o.z.default,i=(0,n.cn)("flex","items-center","justify-center","h-full","fixed","w-full","top-[10%]","pointer-events-none"),l=(0,n.cn)("fixed","opacity-[0.03]","z-[1]");return(0,s.jsx)("div",{className:i,children:(0,s.jsx)("img",{src:a,alt:"",className:l,style:{zoom:"1.2"}})})}},1979:(e,t,a)=>{a.d(t,{z:()=>k});var s=a(4848),n=a(6540),r=a(2090),o=a(4539),i=a(6532),l=a(9696),c=a(7086),d=a(6250),u=a(37),m=a(6555),h=a(7197),p=a(5284);function g({...e}){return(0,s.jsx)(h.bL,{"data-slot":"hover-card",...e})}function f({...e}){return(0,s.jsx)(h.l9,{"data-slot":"hover-card-trigger",...e})}function x({className:e,align:t="center",sideOffset:a=4,...n}){return(0,s.jsx)(h.ZL,{"data-slot":"hover-card-portal",children:(0,s.jsx)(h.UC,{"data-slot":"hover-card-content",align:t,sideOffset:a,className:(0,p.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-64 origin-(--radix-hover-card-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden",e),...n})})}var v=a(888),b=a(2955),y=a(6948),w=a(1319),j=a(1905),N=a(6174),C=a(6508);const S={...C.Af,pre:e=>(0,s.jsx)(C.AC,{...e,wrapperClassName:"my-2",className:(0,p.cn)("bg-[var(--code-bg)] text-[var(--code-text)]",e.className),buttonClassName:"h-7 w-7 text-[var(--text)] hover:bg-[var(--text)]/10"})},k=({triggerOpenCreateModal:e,onModalOpened:t})=>{const[a,h]=(0,n.useState)([]),[C,k]=(0,n.useState)(""),[$,M]=(0,n.useState)(1),[A,E]=(0,n.useState)(null),[T,z]=(0,n.useState)(!1),[P,L]=(0,n.useState)(""),[_,R]=(0,n.useState)(""),[O,I]=(0,n.useState)(""),[D,U]=(0,n.useState)(null),{config:F}=(0,y.UK)(),q=(0,n.useCallback)((async()=>{const e=await(0,b.oK)();h(e)}),[]);(0,n.useEffect)((()=>{q()}),[q]);const W=(0,n.useCallback)((e=>{E(null),L(e?.title||""),R(e?.content||""),I(""),z(!0)}),[]);(0,n.useEffect)((()=>{(async()=>{try{const[e]=await chrome.tabs.query({active:!0,currentWindow:!0});e?.id?(console.log(`[NoteSystemView] Component mounted for tab ${e.id}. Sending SIDE_PANEL_READY signal.`),chrome.runtime.sendMessage({type:"SIDE_PANEL_READY",tabId:e.id},(e=>{chrome.runtime.lastError?console.warn("[NoteSystemView] Could not send ready signal:",chrome.runtime.lastError.message):console.log("[NoteSystemView] Background acknowledged ready signal:",e)}))):console.error("[NoteSystemView] Could not determine the tab ID to send ready signal.")}catch(e){console.error("[NoteSystemView] Error sending ready signal:",e)}})()}),[]),(0,n.useEffect)((()=>{const e=(e,t,a)=>{let s=!1;return"CREATE_NOTE_FROM_PAGE_CONTENT"===e.type&&e.payload?(console.log("[NoteSystemView] Received page data. Storing it in state to trigger auto-save."),U(e.payload),a({status:"PAGE_DATA_QUEUED_FOR_AUTO_SAVE"}),s=!0):"ERROR_OCCURRED"===e.type&&e.payload&&(console.log("[NoteSystemView] Received ERROR_OCCURRED via runtime message."),v.oR.error(String(e.payload)),a({status:"ERROR_DISPLAYED_BY_NOTESYSTEM"}),s=!0),!!s};chrome.runtime.onMessage.addListener(e);const t=chrome.runtime.connect({name:N.A.SidePanelPort});return t.postMessage({type:"init"}),t.onMessage.addListener((e=>{if("ADD_SELECTION_TO_NOTE"===e.type){console.log("[NoteSystemView] Handling ADD_SELECTION_TO_NOTE via port");const t=_?`${_}\n\n${e.payload}`:e.payload;T?R(t):W({content:t,title:"Note with Selection"}),v.oR.success("Selection added to note draft.")}})),()=>{console.log("[NoteSystemView] Cleaning up listeners."),chrome.runtime.onMessage.removeListener(e),t.disconnect()}}),[T,_,W]),(0,n.useEffect)((()=>{(async()=>{if(D){console.log("[NoteSystemView] pendingPageData detected. Attempting automatic save.");const e={...D};if(U(null),!e.content||""===e.content.trim())return void v.oR.error("Cannot save note: Content is empty.");const t={title:e.title.trim()||`Note - ${(new Date).toLocaleDateString()}`,content:e.content,tags:[]};try{await(0,b.s2)(t),v.oR.success("Page added to notes!"),await q()}catch(e){console.error("[NoteSystemView] Error auto-saving note:",e),v.oR.error("Failed to auto-save note.")}}})()}),[D,q]),(0,n.useEffect)((()=>{e&&(W(),t())}),[e,t,W]);const B=(0,n.useMemo)((()=>{if(!C)return a;const e=C.toLowerCase();return a.filter((t=>{const a=t.title.toLowerCase().includes(e),s=t.content.toLowerCase().includes(e),n=t.tags&&t.tags.some((t=>t.toLowerCase().includes(e)));return a||s||n}))}),[a,C]),G=(0,n.useMemo)((()=>{const e=12*($-1);return B.slice(e,e+12)}),[B,$]),V=(0,n.useMemo)((()=>Math.max(1,Math.ceil(B.length/12))),[B]);return(0,s.jsxs)("div",{className:"flex flex-col h-full text-[var(--text)]",children:[(0,s.jsx)("div",{className:"p-0",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(i.p,{type:"text",placeholder:"Search notes (titles & content & tags)...",value:C,onChange:e=>k(e.target.value),className:(0,p.cn)("w-full bg-background text-foreground placeholder:text-muted-foreground font-['Space_Mono',_monospace] pl-10 border-none rounded-none")}),(0,s.jsx)(d.Huy,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground"})]})}),(0,s.jsx)(o.F,{className:"flex-1",children:0===G.length?(0,s.jsx)("p",{className:"text-center text-[var(--muted-foreground)] py-4",children:C?`No notes found for "${C}".`:"No notes yet. Create one!"}):(0,s.jsx)("div",{className:"space-y-0",children:G.map((e=>(0,s.jsx)("div",{className:"px-2 border-b border-[var(--text)]/10 rounded-none hover:shadow-lg transition-shadow w-full",children:(0,s.jsxs)(g,{openDelay:200,closeDelay:100,children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)(f,{asChild:!0,children:(0,s.jsx)("h3",{className:"font-semibold text-md truncate cursor-pointer hover:underline",children:e.title})}),(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsxs)(m.AM,{children:[(0,s.jsx)(m.Wv,{asChild:!0,children:(0,s.jsx)(r.$,{variant:"ghost",size:"sm",children:(0,s.jsx)(u.jbe,{})})}),(0,s.jsxs)(m.hl,{className:"w-30 bg-[var(--popover)] border-[var(--text)]/10 text-[var(--popover-foreground)] mr-1 p-1 space-y-1 shadow-md",children:[(0,s.jsxs)(r.$,{variant:"ghost",className:"w-full justify-start text-md h-8 px-2 font-normal",onClick:()=>(e=>{const t=e.tags?e.tags.join(", "):"";E(e),L(e.title),R(e.content),I(t),z(!0)})(e),children:[(0,s.jsx)(d.i5t,{className:"mr-2 size-4"}),"Edit"]}),(0,s.jsxs)(r.$,{variant:"ghost",className:"w-full justify-start text-md h-8 px-2 font-normal",onClick:()=>{let t="---\n";t+=`title: ${e.title}\n`;const a=e.lastUpdatedAt||e.createdAt;if(a){const e=new Date(a).toISOString().split("T")[0];t+=`date: ${e}\n`}e.tags&&e.tags.length>0&&(t+="tags:\n",e.tags.forEach((e=>{t+=`  - ${e.trim()}\n`}))),t+="---\n\n",t+=e.content;const s=document.createElement("a");s.setAttribute("href",`data:text/markdown;charset=utf-8,${encodeURIComponent(t)}`),s.setAttribute("download",`${e.title}.md`),s.style.display="none",document.body.appendChild(s),s.click(),document.body.removeChild(s)},children:[(0,s.jsx)(d.rII,{className:"mr-2 size-4"}),"ObsidianMD"]}),(0,s.jsxs)(r.$,{variant:"ghost",className:"w-full justify-start text-md h-8 px-2 font-normal text-red-500 hover:text-red-500 hover:bg-red-500/10",onClick:()=>(async e=>{await(0,b.VZ)(e),v.oR.success("Note deleted!"),q()})(e.id),children:[(0,s.jsx)(d.ttk,{className:"mr-2 size-4"})," Delete "]})]})]})})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("p",{className:"text-xs text-[var(--muted-foreground)]",children:["Last updated: ",new Date(e.lastUpdatedAt).toLocaleDateString()]}),e.tags&&e.tags.length>0?(0,s.jsxs)("p",{className:"text-xs text-[var(--muted-foreground)] truncate max-w-[50%]",children:["Tags: ",e.tags.join(", ")]}):(0,s.jsx)("p",{className:"text-xs text-[var(--muted-foreground)]",children:"No tags"})]}),(0,s.jsx)(x,{className:(0,p.cn)("bg-[var(--popover)] border-[var(--active)] text-[var(--popover-foreground)] markdown-body","w-[80vw] sm:w-[70vw] md:w-[50vw] lg:w-[40vw]","max-w-lg","max-h-[70vh]","overflow-y-auto thin-scrollbar"),side:"top",align:"start",children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("h4",{className:"text-sm font-semibold",children:e.title}),(0,s.jsxs)("p",{className:"text-xs text-[var(--muted-foreground)]",children:["Date: ",new Date(e.lastUpdatedAt).toLocaleString()]}),(0,s.jsx)("div",{className:"text-sm whitespace-pre-wrap break-words",children:(0,s.jsx)(w.oz,{remarkPlugins:[j.A],components:S,children:e.content})}),e.tags&&e.tags.length>0&&(0,s.jsxs)("div",{className:"border-t border-[var(--border)] pt-2 mt-2",children:[(0,s.jsx)("p",{className:"text-xs font-semibold text-[var(--text)] mb-1",children:"Tags:"}),(0,s.jsx)("div",{className:"flex flex-wrap gap-1",children:e.tags.map((e=>(0,s.jsx)("span",{className:"text-xs bg-[var(--muted)] text-[var(--muted-foreground)] px-2 py-0.5 rounded",children:e},e)))})]})]})})]})},e.id)))})}),V>1&&(0,s.jsxs)("div",{className:"flex justify-center items-center h-10 space-x-2 p-2 font-['Space_Mono',_monospace]",children:[(0,s.jsx)(r.$,{onClick:()=>M((e=>Math.max(1,e-1))),disabled:1===$,variant:"ghost",className:"font-['Space_Mono',_monospace]",children:"Prev"}),(0,s.jsxs)("span",{className:"text-md",children:["Page ",$," of ",V]}),(0,s.jsx)(r.$,{onClick:()=>M((e=>Math.min(V,e+1))),disabled:$===V,variant:"ghost",className:"font-['Space_Mono',_monospace]",children:"Next"})]}),(0,s.jsx)(c.lG,{open:T,onOpenChange:e=>{e?z(!0):(z(!1),E(null),L(""),R(""),I(""))},children:(0,s.jsxs)(c.Cf,{className:(0,p.cn)("bg-[var(--bg)] border-[var(--text)]/10 w-[90vw] max-w-3xl text-[var(--text)] overflow-hidden","flex flex-col max-h-[85vh]","p-6"),children:[(0,s.jsxs)(c.c7,{children:[(0,s.jsx)(c.L3,{children:A?"Edit Note":"Create New Note"}),(0,s.jsx)(c.rr,{className:"text-[var(--text)]/80 pt-1",children:A?"Update the title or content of your note.":"Provide a title (optional) and content for your new note."})]}),(0,s.jsxs)("div",{className:"flex-1 flex flex-col min-h-0 space-y-4",children:[(0,s.jsx)("div",{children:(0,s.jsx)(i.p,{placeholder:"Note Title (optional)",value:P,onChange:e=>L(e.target.value),className:"bg-[var(--input-bg)] border-[var(--text)]/10 text-[var(--text)]"})}),(0,s.jsx)("div",{className:"flex-1 overflow-y-auto thin-scrollbar min-h-0",children:(0,s.jsx)(l.T,{placeholder:"Your note content...",value:_,onChange:e=>R(e.target.value),autosize:!0,minRows:5,className:"w-full bg-[var(--input-bg)] border-[var(--text)]/10 text-[var(--text)] resize-none overflow-hidden"})}),(0,s.jsx)("div",{children:(0,s.jsx)(i.p,{placeholder:"Tags (comma-separated)",value:O,onChange:e=>{I(e.target.value)},className:"bg-[var(--input-bg)] border-[var(--text)]/10 text-[var(--text)]"})})]}),(0,s.jsxs)(c.Es,{children:[(0,s.jsx)(r.$,{variant:"outline",onClick:()=>{z(!1),E(null)},children:"Cancel"}),(0,s.jsx)(r.$,{onClick:async()=>{if(!_.trim())return void v.oR.error("Note content cannot be empty.");const e=""===O.trim()?[]:O.split(",").map((e=>e.trim())).filter((e=>e.length>0)),t={id:A?.id,title:P.trim()||`Note - ${(new Date).toLocaleDateString()}`,content:_,tags:e};await(0,b.s2)(t),v.oR.success(A?"Note updated!":"Note created!"),await q(),z(!1),E(null),L(""),R(""),I("")},className:"bg-[var(--active)] text-[var(--active-foreground)] hover:bg-[var(--active)]/90",children:A?"Save Changes":"Create Note"})]})]})})]})}},2090:(e,t,a)=>{a.d(t,{$:()=>l});var s=a(4848),n=(a(6540),a(3362)),r=a(2732),o=a(5284);const i=(0,r.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none shrink-0 [&_svg]:shrink-0 outline-none not-focus-visible",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-xs hover:bg-destructive/90 not-focus-visible",outline:"border bg-background shadow-xs hover:bg-accent",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:text-foreground hover:bg-black/10 dark:hover:bg-white/10",link:"text-primary underline-offset-4 hover:underline","message-action":"bg-transparent text-muted-foreground p-0 shadow-none hover:bg-transparent focus:bg-transparent active:text-muted active:[&_svg]:text-muted-foreground transition-colors duration-75","ghost-themed":"text-foreground hover:bg-accent hover:text-accent-foreground focus-visible:bg-accent focus-visible:text-accent-foreground","outline-themed":"border border-[var(--active)] bg-transparent text-[var(--active)] shadow-xs hover:bg-[var(--active)]/20 focus-visible:bg-[var(--active)]/20 focus-visible:ring-1 focus-visible:ring-[var(--active)]","destructive-outline":"border border-destructive bg-transparent text-destructive shadow-xs hover:bg-destructive/10 hover:text-destructive-foreground focus-visible:bg-destructive/10 focus-visible:text-destructive-foreground focus-visible:ring-1 focus-visible:ring-destructive","copy-button":"bg-background text-foreground shadow-none hover:bg-accent hover:text-accent-foreground focus-visible:bg-accent focus-visible:text-accent-foreground",connect:"bg-[var(--input-background)] text-[var(--text)] hover:bg-[var(--active)]/90 shadow-sm focus-visible:ring-1 focus-visible:ring-[var(--ring)] focus-visible:ring-offset-1 focus-visible:ring-offset-[var(--bg)]","active-bordered":"bg-[var(--active)] text-[var(--text)] border border-[var(--text)] hover:brightness-110 focus-visible:ring-1 focus-visible:ring-[var(--active)] focus-visible:ring-offset-1 focus-visible:ring-offset-[var(--bg)] shadow-sm","outline-subtle":"border border-[var(--text)]/50 bg-transparent text-[var(--text)] hover:bg-[var(--text)]/10 hover:border-[var(--text)]/70 hover:text-[var(--text)] focus-visible:ring-1 focus-visible:ring-[var(--active)] focus-visible:ring-offset-1 focus-visible:ring-offset-[var(--bg)] shadow-sm"},size:{default:"h-9 px-2 py-2 has-[>svg]:px-2 [&_svg:not([class*='size-'])]:size-5",sm:"h-8 rounded-md px-2 has-[>svg]:px-2 [&_svg:not([class*='size-'])]:size-4",lg:"h-10 rounded-md px-2 py-2 has-[>svg]:px-2 [&_svg:not([class*='size-'])]:size-5",icon:"size-8 [&_svg:not([class*='size-'])]:size-7",xs:"h-6 w-6 p-0 rounded-sm [&_svg:not([class*='size-'])]:size-3.5 text-xs"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:a,asChild:r=!1,...l}){const c=r?n.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,o.cn)(i({variant:t,size:a,className:e})),...l})}},2951:(e,t,a)=>{a.d(t,{hL:()=>i,GW:()=>s,hj:()=>l,tE:()=>o});const s=async(e,t,a,s,n,r=[],o)=>{try{if(!a?.host)return console.error("processQueryWithAI: currentModel or currentModel.host is undefined. Cannot determine API URL."),e;const i=r.map((e=>`{{${e.role}}}: ${e.content}`)).join("\n"),l=`You are a Google search query optimizer. Your task is to rewrite user's input [The user's raw input && chat history:${i}].\n\n\nInstructions:\n**Important** No Explanation, just the optimized query!\n\n\n1. Extract the key keywords and named entities from the user's input.\n2. Correct any obvious spelling errors.\n3. Remove unnecessary words (stop words) unless they are essential for the query's meaning.\n4. If the input is nonsensical or not a query, return the original input.\n5. Using previous chat history to understand the user's intent.\n\n\nOutput:\n'The optimized Google search query'\n\n\nExample 1:\nInput from user ({{user}}): where can i find cheep flights to london\nOutput:\n'cheap flights London'\n\n\nExample 2:\nContext: {{user}}:today is a nice day in paris i want to have a walk and find a restaurant to have a nice meal. {{assistant}}: Bonjour, it's a nice day!\nInput from user ({{user}}): please choose me the best restarant\nOutput:\n'best restaurants Paris France'\n\n\nExample 3:\nInput from user ({{user}}): asdf;lkjasdf\nOutput:\n'asdf;lkjasdf'\n`,c={ollama:`${t?.ollamaUrl||""}/api/chat`}[a.host];if(!c)return console.error("processQueryWithAI: Could not determine API URL for host:",a.host),e;console.log(`processQueryWithAI: Using API URL: ${c} for host: ${a.host}`),console.log("Formatted Context for Prompt:",i);const d={model:t?.selectedModel||a.id||"",messages:[{role:"system",content:l},{role:"user",content:e}],stream:!1};let u;void 0!==o?u=o:void 0!==t.temperature&&(u=t.temperature),void 0!==u&&(d.temperature=u);const m=await fetch(c,{method:"POST",headers:{"Content-Type":"application/json",...s||{}},signal:n,body:JSON.stringify(d)});if(!m.ok){const e=await m.text();throw console.error(`API request failed with status ${m.status}: ${e}`),new Error(`API request failed: ${m.statusText}`)}const h=await m.json(),p=h?.choices?.[0]?.message?.content;return"string"==typeof p?(e=>e.replace(/<think>[\s\S]*?<\/think>/g,"").replace(/["']/g,"").trim())(p):e}catch(t){if(n?.aborted||t instanceof Error&&"AbortError"===t.name)throw console.log("processQueryWithAI: Operation aborted."),t;return console.error("processQueryWithAI: Error during execution:",t),e}},n=async function(e){try{const t=new URL(e);if("chrome:"===t.protocol)return;const a=[{id:1,priority:1,condition:{requestDomains:[t.hostname]},action:{type:"modifyHeaders",requestHeaders:[{header:"Origin",operation:"set",value:`${t.protocol}//${t.hostname}`}]}}];await chrome.declarativeNetRequest.updateDynamicRules({removeRuleIds:a.map((e=>e.id)),addRules:a})}catch(e){console.debug("URL rewrite skipped:",e)}},r=e=>{try{const t=(new DOMParser).parseFromString(e,"text/html");t.querySelectorAll('script, style, nav, footer, header, svg, img, noscript, iframe, form, aside, .sidebar, .ad, .advertisement, .banner, .popup, .modal, .cookie-banner, link[rel="stylesheet"], button, input, select, textarea, [role="navigation"], [role="banner"], [role="contentinfo"], [aria-hidden="true"]').forEach((e=>e.remove()));let a=t.querySelector("main")||t.querySelector("article")||t.querySelector(".content")||t.querySelector("#content")||t.querySelector(".main-content")||t.querySelector("#main-content")||t.querySelector(".post-content")||t.body,s=a?.textContent||"";return s=s.replace(/\s+/g," ").trim(),s=s.split("\n").filter((e=>e.trim().length>20)).join("\n"),s}catch(e){return console.error("Error parsing HTML for content extraction:",e),"[Error extracting content]"}},o=async(e,t,a)=>{console.log("[webSearch] Received query:",e),console.log("[webSearch] Web Mode from config:",t?.webMode);const s=t.webMode,n=t.serpMaxLinksToVisit??3,o=t.webLimit&&128!==t.webLimit?1e3*t.webLimit:1/0,i=a||(new AbortController).signal;if(console.log(`Performing ${s} search for: "${e}"`),"Google"===s&&console.log(`[webSearch - ${s}] Max links to visit for content scraping: ${n}`),!s)return console.error("[webSearch] Web search mode is undefined. Aborting search. Config was:",JSON.stringify(t)),"Error: Web search mode is undefined. Please check your configuration.";try{if("Google"===s){const t=new AbortController,a=setTimeout((()=>{console.warn(`[webSearch - ${s}] SERP API call timed out after 15s.`),t.abort()}),15e3),l="function"==typeof AbortSignal.any?AbortSignal.any([i,t.signal]):i,c=`https://www.google.com/search?q=${encodeURIComponent(e)}&hl=en&gl=us&num=10&start=0&safe=off&filter=0`;console.log(`[webSearch - ${s}] Fetching SERP from: ${c}`);const d=await fetch(c,{signal:l,method:"GET",headers:{"User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",Accept:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","Accept-Language":"en-US,en;q=0.9","Accept-Encoding":"gzip, deflate, br",DNT:"1",Connection:"keep-alive","Upgrade-Insecure-Requests":"1","Sec-Fetch-Dest":"document","Sec-Fetch-Mode":"navigate","Sec-Fetch-Site":"none","Sec-Fetch-User":"?1","Cache-Control":"max-age=0",Referer:"https://www.google.com/"}}).finally((()=>{clearTimeout(a)}));if(console.log(`[webSearch - ${s}] Response status: ${d.status} ${d.statusText}`),console.log(`[webSearch - ${s}] Response headers:`,Object.fromEntries(d.headers.entries())),!d.ok){const e=await d.text();throw console.error(`[webSearch - ${s}] Error response body:`,e.substring(0,1e3)),429===d.status?new Error("Google search rate limited (429). Please try again later."):403===d.status?new Error("Google search access forbidden (403). Google may be blocking automated requests."):d.status>=400&&d.status<500?new Error(`Google search client error (${d.status}): ${d.statusText}`):new Error(`Google search server error (${d.status}): ${d.statusText}`)}if(i.aborted)throw new Error("Web search operation aborted.");const u=await d.text();if(console.log(`[webSearch - ${s}] SERP HTML length: ${u.length} characters`),console.log(`[webSearch - ${s}] SERP HTML (first 500 chars):`,u.substring(0,500)),u.includes("captcha")||u.includes("unusual traffic")||u.includes("blocked"))return console.warn(`[webSearch - ${s}] Detected potential CAPTCHA or blocking page`),"Error: Google search may be blocked. The page contains CAPTCHA or blocking content. Please try again later.";const m=(new DOMParser).parseFromString(u,"text/html"),h=[],p=["div.g","div.MjjYud","div.hlcw0c","div.kvH3mc","div.tF2Cxc","div.yuRUbf"];let g=[];for(const e of p){const t=Array.from(m.querySelectorAll(e));if(t.length>0){g=t,console.log(`[webSearch - ${s}] Found ${t.length} results using selector: ${e}`);break}}if(0===g.length&&(g=Array.from(m.querySelectorAll("div[data-ved], div[data-hveid]")),console.log(`[webSearch - ${s}] Fallback: Found ${g.length} results using data attributes`)),g.forEach(((e,t)=>{try{let a=e.querySelector('a[href^="http"]')||e.querySelector('a[href^="/url?q="]')||e.querySelector("a[href]"),n=a?.getAttribute("href");if(n&&n.startsWith("/url?q=")){const e=new URLSearchParams(n.substring(6));n=e.get("q")||n}const r=e.querySelector("h3")||e.querySelector("h2")||e.querySelector('[role="heading"]')||e.querySelector("a[href] > div"),o=r?.textContent?.trim()||"";let i="";const l=['div[style*="-webkit-line-clamp"]','div[data-sncf="1"]',".VwiC3b span",".MUxGbd span",".s3v9rd",".st",'span[style*="-webkit-line-clamp"]'];for(const t of l){const a=e.querySelectorAll(t);if(a.length>0){i=Array.from(a).map((e=>e.textContent)).join(" ").replace(/\s+/g," ").trim();break}}if(!i&&o){const t=e.textContent||"",a=t.indexOf(o);-1!==a&&(i=t.substring(a+o.length).replace(/\s+/g," ").trim().substring(0,300))}o&&n&&n.startsWith("http")&&!n.includes("google.com/search")&&(h.push({title:o,snippet:i,url:n}),console.log(`[webSearch - ${s}] Parsed result ${t+1}: ${o.substring(0,50)}...`))}catch(e){console.warn(`[webSearch - ${s}] Error parsing result ${t+1}:`,e)}})),console.log(`[webSearch - ${s}] Parsed SERP Results (${h.length} found, showing first 5):`,JSON.stringify(h.slice(0,5))),0===h.length){console.warn(`[webSearch - ${s}] No search results found on SERP.`),console.log(`[webSearch - ${s}] HTML document title:`,m.title),console.log(`[webSearch - ${s}] Available div elements:`,Array.from(m.querySelectorAll("div")).slice(0,10).map((e=>({className:e.className,id:e.id,textContent:e.textContent?.substring(0,100)}))));const e=m.body?.textContent?.toLowerCase()||"";return e.includes("captcha")||e.includes("unusual traffic")||e.includes("blocked")||e.includes("robot")?"Error: Google search appears to be blocked. The page contains CAPTCHA or blocking content. Please try again later or check if the extension has proper permissions.":"No search results found. Google may have changed their page structure or the search was unsuccessful."}const f=h.slice(0,n).filter((e=>e.url));console.log(`Found ${h.length} results. Attempting to fetch content from top ${f.length} links (maxLinksToVisit: ${n}).`);const x=f.map((async e=>{if(!e.url)return{...e,content:"[Invalid URL]",status:"error"};if(i.aborted)return{...e,content:`[Fetching aborted by user: ${e.url}]`,status:"aborted"};console.log(`Fetching content from: ${e.url}`);const t=new AbortController,a=setTimeout((()=>{console.warn(`[webSearch] Page scrape for ${e.url} timed out after 12s.`),t.abort()}),12e3),n="function"==typeof AbortSignal.any?AbortSignal.any([i,t.signal]):i;let o=`[Error fetching/processing: Unknown error for ${e.url}]`,l="error";try{const t=await fetch(e.url,{signal:n,method:"GET",headers:{"User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",Accept:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8","Accept-Language":"en-US,en;q=0.9","Accept-Encoding":"gzip, deflate, br",DNT:"1",Connection:"keep-alive","Upgrade-Insecure-Requests":"1","Sec-Fetch-Dest":"document","Sec-Fetch-Mode":"navigate","Sec-Fetch-Site":"cross-site","Cache-Control":"max-age=0"}});if(!t.ok)throw new Error(`Failed to fetch ${e.url} - Status: ${t.status}`);const a=t.headers.get("content-type");if(!a||!a.includes("text/html"))throw new Error(`Skipping non-HTML content (${a}) from ${e.url}`);if(i.aborted)throw new Error("Web search operation aborted by user.");const c=await t.text();o=r(c),l="success",console.log(`[webSearch - ${s}] Successfully fetched and extracted content from: ${e.url} (Extracted Length: ${o.length})`)}catch(a){if("AbortError"===a.name){if(i.aborted)throw a;o=t.signal.aborted?`[Timeout fetching: ${e.url}]`:`[Fetching aborted: ${e.url}]`,l="aborted"}else o=`[Error fetching/processing: ${a.message}]`,l="error"}finally{clearTimeout(a)}return{...e,content:o,status:l}})),v=await Promise.allSettled(x);if(i.aborted)throw new Error("Web search operation aborted.");let b=`Search results for "${e}" using ${s}:\n\n`,y=0;return h.forEach(((e,t)=>{if(b+=`[Result ${t+1}: ${e.title}]\n`,b+=`URL: ${e.url||"[No URL Found]"}\n`,b+=`Snippet: ${e.snippet||"[No Snippet]"}\n`,t<f.length){const t=v[y];if("fulfilled"===t?.status){const a=t.value;if(a.url===e.url){const e=a.content.substring(0,o);b+=`Content:\n${e}${a.content.length>o?"...":""}\n\n`}else b+=`Content: [Content fetch mismatch - data for ${a.url} found, expected ${e.url}]\n\n`}else b+="rejected"===t?.status?`Content: [Error fetching: ${t.reason}]\n\n`:"Content: [Fetch status unknown]\n\n";y++}else b+="Content: [Not fetched due to link limit]\n\n"})),console.log("Web search finished. Returning combined results."),b.trim()}return`Unsupported web search mode: ${s}`}catch(e){if("AbortError"===e.name&&i.aborted)throw console.log("[webSearch] Operation aborted by signal."),e;return console.error("Web search overall failed:",e),`Error performing web search: ${e.message}`}};async function i(e,t,a,s={},r,o){let i=!1;const l=(e,t=!1)=>{if(!i){let s;i=!0,s="string"==typeof e?e:e&&"object"==typeof e&&"message"in e&&"string"==typeof e.message?e.message:String(e),a(s,!0,t)}},c=()=>{if(o?.aborted)throw new Error("Streaming operation aborted by user.")};if(e.startsWith("chrome://"))console.log("fetchDataAsStream: Skipping chrome:// URL:",e);else{e.includes("localhost")&&await n((e=>e.endsWith("/")?e.slice(0,-1):e)(e));try{const n=await fetch(e,{method:"POST",headers:{"Content-Type":"application/json",...s},body:JSON.stringify(t),signal:o});if(!n.ok){let e=`Network response was not ok (${n.status})`;try{e+=`: ${await n.text()||n.statusText}`}catch(t){e+=`: ${n.statusText}`}throw new Error(e)}let d="";if("ollama"!==r)throw new Error(`Unsupported host specified: ${r}`);{if(!n.body)throw new Error("Response body is null for Ollama");const e=n.body.getReader();let t,s;for(;c(),({value:s,done:t}=await e.read()),!t;){const t=(new TextDecoder).decode(s).split("\n").filter((e=>""!==e.trim()));for(const s of t){if("[DONE]"===s.trim())return o?.aborted&&e.cancel(),void l(d);try{const t=JSON.parse(s);if(t.message?.content&&(d+=t.message.content,i||a(d)),!0===t.done&&!i)return o?.aborted&&e.cancel(),void l(d)}catch(e){console.debug("Skipping invalid JSON chunk:",s)}}}o?.aborted&&e.cancel(),l(d)}}catch(e){o?.aborted?(console.log("[fetchDataAsStream] Operation aborted via signal as expected. Details:",e),l("",!1)):e instanceof Error&&"AbortError"===e.name?(console.log("[fetchDataAsStream] AbortError (name check) caught. Operation was cancelled. Details:",e),l("",!1)):(console.error("Error in fetchDataAsStream (unexpected):",e),l(e instanceof Error?e.message:String(e),!0))}}}async function l(e,t){const a=new AbortController,s=t||a.signal,n=t?null:setTimeout((()=>a.abort()),12e3);try{const t=await fetch(e,{signal:s,method:"GET",headers:{"User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",Accept:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8","Accept-Language":"en-US,en;q=0.9","Accept-Encoding":"gzip, deflate, br",DNT:"1",Connection:"keep-alive","Upgrade-Insecure-Requests":"1","Sec-Fetch-Dest":"document","Sec-Fetch-Mode":"navigate","Sec-Fetch-Site":"cross-site","Cache-Control":"max-age=0"}});if(n&&clearTimeout(n),s.aborted)throw new Error("Scraping aborted by user.");if(!t.ok)throw new Error(`Failed to fetch ${e} - Status: ${t.status}`);const a=t.headers.get("content-type");if(!a||!a.includes("text/html"))throw new Error(`Skipping non-HTML content (${a}) from ${e}`);const o=await t.text();return r(o)}catch(t){return n&&clearTimeout(n),"AbortError"===t.name?`[Scraping URL aborted: ${e}]`:`[Error scraping URL: ${e} - ${t.message}]`}}"undefined"!=typeof window&&(window.testGoogleSearchDebug=async function(e="test search"){console.log("🔍 Testing Google search functionality...");const t={webMode:"Google",serpMaxLinksToVisit:2,webLimit:16,personas:{},persona:"Scholar",contextLimit:60,temperature:.7,maxTokens:32480,topP:.95,presencepenalty:0,models:[],chatMode:"web",ollamaUrl:"http://localhost:11434",ollamaConnected:!1,fontSize:14,panelOpen:!1,computeLevel:"low",useNote:!1,noteContent:"",userName:"user",userProfile:"",popoverTitleDraft:"",popoverTagsDraft:""};try{const a=await o(e,t);return console.log("✅ Search completed successfully!"),console.log("📄 Result length:",a.length),console.log("📋 Result preview:",a.substring(0,500)+"..."),a}catch(e){throw console.error("❌ Search failed:",e),e}})},2955:(e,t,a)=>{a.d(t,{VZ:()=>l,oK:()=>i,s2:()=>o});var s=a(3790),n=a.n(s);const r="cognito_note_",o=async e=>{const t=Date.now(),a=e.id||`${r}${Date.now()}_${Math.random().toString(16).slice(2)}`,s=e.id?await n().getItem(a):null,o={id:a,title:e.title||`Note - ${new Date(t).toLocaleDateString([],{year:"numeric",month:"numeric",day:"numeric",hour:"2-digit",minute:"2-digit"})}`,content:e.content,createdAt:s?.createdAt||t,lastUpdatedAt:t,tags:e.tags};return await n().setItem(a,o),o},i=async()=>{const e=(await n().keys()).filter((e=>e.startsWith(r))),t=[];for(const a of e){const e=await n().getItem(a);e&&t.push(e)}return t.sort(((e,t)=>t.lastUpdatedAt-e.lastUpdatedAt))},l=async e=>{await n().removeItem(e),console.log("Note deleted from system:",e)}},3003:(e,t,a)=>{a.a(e,(async(e,t)=>{try{var s=a(4848),n=a(5338),r=a(1468),o=a(3190),i=a(6174),l=a(9828),c=a(6948),d=e([l]);l=(d.then?(await d)():d)[0];const u=(0,o.g)(i.A.ContentPort),m=document.getElementById("root");u.ready().then((()=>{if(null==m)throw new Error("Root container not found");(0,n.createRoot)(m).render((0,s.jsx)(r.Kq,{store:u,children:(0,s.jsx)(c.sG,{children:(0,s.jsx)(l.A,{})})}))})),t()}catch(e){t(e)}}))},3108:(e,t,a)=>{a.d(t,{Y:()=>O});var s=a(4848),n=a(6540),r=a(3),o=a(5066),i=a(6948),l=a(5284),c=a(888),d=a(2090),u=a(990),m=a(8697);function h({...e}){return(0,s.jsx)(u.bL,{"data-slot":"sheet",...e})}function p({...e}){return(0,s.jsx)(u.ZL,{"data-slot":"sheet-portal",...e})}function g({className:e,...t}){return(0,s.jsx)(u.hJ,{"data-slot":"sheet-overlay",className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}const f=n.forwardRef((({className:e,children:t,side:a="right",variant:n="default",...r},o)=>{const i="themedPanel"===n?((e="right")=>(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===e&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full border-l","left"===e&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full border-r","top"===e&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===e&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t","bg-[var(--bg)] text-[var(--text)] shadow-xl"))(a):((e="right")=>(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===e&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===e&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===e&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===e&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t"))(a);return(0,s.jsxs)(p,{children:[(0,s.jsx)(g,{})," ",(0,s.jsxs)(u.UC,{ref:o,"data-slot":"sheet-content",className:(0,l.cn)(i,e),...r,children:[t,(0,s.jsxs)(u.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,s.jsx)(m.A,{className:"size-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}));function x({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"sheet-header",className:(0,l.cn)("flex flex-col gap-1.5 p-4",e),...t})}function v({className:e,...t}){return(0,s.jsx)(u.hE,{"data-slot":"sheet-title",className:(0,l.cn)("text-foreground font-semibold",e),...t})}function b({className:e,...t}){return(0,s.jsx)(u.VY,{"data-slot":"sheet-description",className:(0,l.cn)("text-muted-foreground text-sm",e),...t})}f.displayName=u.UC.displayName;var y=a(9018),w=a(6532),j=a(3885),N=a(69),C=a(8698),S=a(7520);const k=()=>{const{config:e}=(0,i.UK)(),t=(0,n.useRef)(null);return(0,n.useEffect)((()=>{if(!e?.animatedBackground)return;const a=t.current;if(a){a.innerHTML="";{const s=document.createElement("canvas");a.appendChild(s);const n=s.getContext("2d");if(!n)return;const r=16,o=1.2*r,i=1.2*r;function l(){s.width=window.innerWidth,s.height=window.innerHeight}l(),window.addEventListener("resize",l);const c=["N","ﾐ","ﾋ","𐌊","ｳ","ｼ","ﾅ","𐌔","X","ｻ","ﾜ","ㄘ","𑖖","𑖃","𑖆","𐌈","J","ｱ","ﾎ","ﾃ","M","π","Σ","Y","ｷ","ㄠ","ﾕ","ﾗ","ｾ","ﾈ","Ω","ﾀ","ﾇ","ﾍ","ｦ","ｲ","ｸ","W","𐌙","ﾁ","ﾄ","ﾉ","Δ","ﾔ","ㄖ","ﾙ","ﾚ","王","道","Ж","ﾝ","0","1","2","3","4","5","7","8","9","A","B","Z","*","+","д","Ⱟ","𑗁","T","|","ç","ﾘ","Ѯ"],d=["#15803d","#16a34a","#22c55e","#4ade80"],u="#f0fdf4";let m=Math.floor(s.width/o),h=Math.floor(s.height/i),p=Array(m).fill(0),g=Array(m).fill(null).map((()=>Array(h).fill({char:"",color:""}))),f=Array(m).fill(0).map((()=>Math.floor(2*Math.random())+1)),x=Array(m).fill(0);const v=12;function b(){n.clearRect(0,0,s.width,s.height),n.font=`${r}px monospace`,n.textAlign="center",n.textBaseline="top";for(let e=0;e<m;e++){for(let t=0;t<v;t++){const a=p[e]-t;if(a<0)continue;if(a>=h)continue;let s=g[e][a];s&&s.char&&(n.fillStyle=0===t?u:s.color,n.globalAlpha=.3*(1-t/v),n.fillText(s.char,e*o+o/2,a*i))}if(n.globalAlpha=1,x[e]++,x[e]>=f[e]){const t=c[Math.floor(Math.random()*c.length)],a=d[Math.floor(Math.random()*d.length)];g[e][p[e]]={char:t,color:a},p[e]++,p[e]>=h+v&&(p[e]=0,g[e]=Array(h).fill({char:"",color:""}),f[e]=Math.floor(10*Math.random())+10),x[e]=0}}requestAnimationFrame(b)}b();const y=()=>{l(),m=Math.floor(s.width/o),h=Math.floor(s.height/i),p=Array(m).fill(0)};return window.addEventListener("resize",y),()=>{window.removeEventListener("resize",l),window.removeEventListener("resize",y),a.removeChild(s)}}}}),[e?.animatedBackground]),e?.animatedBackground?(0,s.jsx)("div",{ref:t,style:{position:"absolute",top:0,left:0,width:"100%",height:"100%",zIndex:-1,pointerEvents:"none",overflow:"hidden"}}):null},$=({isOpen:e,onOpenChange:t,config:a,updateConfig:o,setSettingsMode:i,setHistoryMode:c,setNoteSystemMode:u})=>{const[m,p]=n.useState(""),[$,M]=n.useState(!1),{fetchAllModels:A}=(0,C.N)(),E=n.useRef(null),T=(0,n.useRef)(null),[z,P]=n.useState({top:0,left:0,width:0}),L=a?.persona||"default",_=S.z[L]||S.z.default,R=a?.models?.filter((e=>e.id.toLowerCase().includes(m.toLowerCase())||e.host?.toLowerCase()?.includes(m.toLowerCase())))||[];return(0,n.useEffect)((()=>{e&&(p(""),M(!1))}),[e]),(0,n.useEffect)((()=>{if($&&T.current){const e=T.current.getBoundingClientRect();P({top:e.bottom+window.scrollY,left:e.left+window.scrollX,width:e.width})}}),[$]),(0,n.useEffect)((()=>{if(!$)return;const e=()=>{if(T.current){const e=T.current.getBoundingClientRect();P({top:e.bottom+window.scrollY,left:e.left+window.scrollX,width:e.width})}};return window.addEventListener("resize",e),window.addEventListener("scroll",e,!0),()=>{window.removeEventListener("resize",e),window.removeEventListener("scroll",e,!0)}}),[$]),(0,s.jsxs)(h,{open:e,onOpenChange:t,children:[(0,s.jsx)(g,{}),(0,s.jsxs)(f,{variant:"themedPanel",side:"left",className:(0,l.cn)("p-0 border-r-0","w-[22.857rem] sm:w-[27.143rem]","flex flex-col h-full max-h-screen","[&>button]:hidden","settings-drawer-content","overflow-y-auto"),style:{height:"100dvh"},ref:E,onOpenAutoFocus:e=>{e.preventDefault(),E.current?.focus({preventScroll:!0})},children:[(0,s.jsx)(k,{}),(0,s.jsx)("div",{className:(0,l.cn)("border border-[var(--active)]","sticky top-0 z-10 p-0")}),(0,s.jsxs)(x,{className:"px-4 pt-4 pb-4",children:[(0,s.jsx)("div",{className:"flex items-center justify-between mb-2 relative z-10",children:(0,s.jsxs)(j.m_,{children:[(0,s.jsx)(j.k$,{asChild:!0,children:(0,s.jsx)(d.$,{variant:"ghost",size:"sm","aria-label":"Close Settings",className:"text-[var(--text)] rounded-md relative top-[1px]",onClick:()=>t(!1),children:(0,s.jsx)(r.yGN,{size:"20px"})})}),(0,s.jsx)(j.ZI,{side:"bottom",className:"bg-[var(--active)]/50 text-[var(--text)] border-[var(--text)]",children:" Close Settings "})]})}),(0,s.jsx)(v,{className:"text-center font-['Bruno_Ace_SC'] tracking-tight -mt-10 cognito-title-container",children:(0,s.jsxs)("a",{href:"https://github.com/3-ark/Cognito",target:"_blank",rel:"noopener noreferrer",className:(0,l.cn)("text-xl font-semibold text-[var(--text)] bg-[var(--active)] inline-block px-3 py-1 rounded-md no-underline","chromepanion-title-blade-glow"),children:["CHROMEPANION ",(0,s.jsxs)("sub",{className:"contrast-200 text-[0.5em]",children:["v","3.7.4"]})]})}),(0,s.jsx)(b,{className:"text-center font-['Bruno_Ace_SC'] text-[var(--text)] leading-tight mt-2",children:"Settings"})]}),(0,s.jsxs)("div",{className:(0,l.cn)("flex flex-col h-full overflow-y-auto settings-drawer-body","no-scrollbar"),children:[(0,s.jsxs)("div",{className:(0,l.cn)("flex flex-col space-y-5 flex-1","px-6","py-4"),children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"flex items-center justify-between mt-5 mb-3",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("label",{htmlFor:"persona-select",className:"text-[var(--text)] opacity-80 font-['Bruno_Ace_SC'] text-lg shrink-0",children:"Persona"}),(0,s.jsxs)(N.eu,{className:"h-8 w-8 border border-[var(--active)]",children:[(0,s.jsx)(N.BK,{src:_,alt:L}),(0,s.jsx)(N.q5,{children:L.substring(0,1).toUpperCase()})]})]})}),(0,s.jsx)("div",{className:"w-full",children:(0,s.jsxs)(y.l6,{value:L,onValueChange:e=>o({persona:e}),children:[(0,s.jsx)(y.bq,{id:"persona-select",variant:"settingsPanel",className:"w-full font-['Space_Mono',_monospace] data-[placeholder]:text-muted-foreground",children:(0,s.jsx)(y.yv,{placeholder:"Select Persona..."})}),(0,s.jsx)(y.gC,{variant:"settingsPanel",children:Object.keys(a?.personas||{}).map((e=>(0,s.jsx)(y.eb,{value:e,className:(0,l.cn)("hover:brightness-95 focus:bg-[var(--active)]","font-['Space_Mono',_monospace]"),children:e},e)))})]})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"model-input",className:"block text-[var(--text)] opacity-80 text-lg font-['Bruno_Ace_SC']",children:"Model"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(w.p,{id:"model-input",ref:T,value:$?m:a?.selectedModel||"",placeholder:$?"Search models...":a?.selectedModel||"Select model...",onChange:e=>p(e.target.value),onFocus:()=>{p(""),M(!0),A()},className:(0,l.cn)("text-[var(--text)] rounded-xl shadow-md w-full justify-start font-medium h-9 font-['Space_Mono',_monospace]","focus:border-[var(--active)] focus:ring-1 focus:ring-[var(--active)]","hover:border-[var(--active)] hover:brightness-95","mb-2 mt-3","ring-1 ring-inset ring-[var(--active)]/50")}),$&&(0,s.jsx)("div",{className:"fixed inset-0 z-50",onClick:()=>M(!1),children:(0,s.jsx)("div",{className:(0,l.cn)("absolute left-0 right-0","bg-[var(--bg)]","border border-[var(--active)]/20","rounded-xl shadow-lg","no-scrollbar","overflow-y-auto"),style:{maxHeight:"min(calc(50vh - 6rem), 300px)",top:`${z.top}px`,left:`${z.left}px`,width:`${z.width}px`},onClick:e=>e.stopPropagation(),children:(0,s.jsx)("div",{className:"py-0.5",children:R.length>0?R.map((e=>(0,s.jsx)("button",{type:"button",className:(0,l.cn)("w-full text-left","px-4 py-1.5","text-[var(--text)] text-sm","hover:bg-[var(--active)]/20","focus:bg-[var(--active)]/30","transition-colors duration-150","font-['Space_Mono',_monospace]"),onClick:()=>{o({selectedModel:e.id}),p(""),M(!1)},children:(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsxs)("span",{children:[e.host?`(${e.host}) `:"",e.id,e.context_length&&(0,s.jsxs)("span",{className:"text-xs text-[var(--text)] opacity-50 ml-1",children:["[ctx: ",e.context_length,"]"]})]})})},e.id))):(0,s.jsx)("div",{className:"px-4 py-1.5 text-[var(--text)] opacity-50 text-sm",children:"No models found"})})})})]})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)(d.$,{size:"default",onClick:()=>{i(!0),t(!1)},variant:"outline",className:(0,l.cn)("text-[var(--text)] rounded-xl shadow-md w-full justify-start font-medium h-9","bg-[rgba(255,250,240,0.4)] dark:bg-[rgba(255,255,255,0.1)]","border-[var(--text)]/10","font-['Space_Mono',_monospace]","hover:border-[var(--active)] hover:brightness-98 active:bg-[var(--active)] active:brightness-95","focus:ring-1 focus:ring-[var(--active)]","mb-4"),children:"Configuration"}),(0,s.jsx)(d.$,{variant:"outline",size:"default",onClick:()=>{c(!0),t(!1)},className:(0,l.cn)("text-[var(--text)] rounded-xl shadow-md w-full justify-start font-medium h-9","bg-[rgba(255,250,240,0.4)] dark:bg-[rgba(255,255,255,0.1)]","border-[var(--text)]/10","font-['Space_Mono',_monospace]","hover:border-[var(--active)] hover:brightness-98 active:bg-[var(--active)] active:brightness-95","focus:ring-1 focus:ring-[var(--active)]","mb-4 mt-3"),children:"Chat History"}),(0,s.jsx)(d.$,{variant:"outline",size:"default",onClick:()=>{u(!0),t(!1)},className:(0,l.cn)("text-[var(--text)] rounded-xl shadow-md w-full justify-start font-medium h-9","bg-[rgba(255,250,240,0.4)] dark:bg-[rgba(255,255,255,0.1)]","border-[var(--text)]/10","font-['Space_Mono',_monospace]","hover:border-[var(--active)] hover:brightness-98 active:bg-[var(--active)] active:brightness-95","focus:ring-1 focus:ring-[var(--active)]","mb-4 mt-3"),children:"Note System"})]})]}),(0,s.jsx)("div",{className:(0,l.cn)("mt-auto text-center text-[var(--text)] opacity-70 shrink-0 text-xs font-mono pb-4"),children:"Made with ❤️ by @3-Arc"})]})]})]})};var M=a(7086),A=a(5634),E=a(3720),T=a(3732),z=a(6250),P=a(6973);const L=({isOpen:e,onClose:t,setSettingsMode:a})=>(0,s.jsx)(M.lG,{open:e,onOpenChange:t,children:(0,s.jsxs)(M.Cf,{variant:"themedPanel",className:(0,l.cn)("[&>button]:hidden","bg-card border border-border shadow-lg"),style:{width:"20rem",height:"12rem",borderRadius:"var(--radius-lg)",boxShadow:"var(--shadow-lg)"},onInteractOutside:e=>e.preventDefault(),children:[(0,s.jsx)(M.c7,{className:"text-center p-4",children:(0,s.jsx)(M.L3,{className:"text-apple-title3 text-foreground",children:"Welcome to Chromepanion"})}),(0,s.jsx)(M.rr,{asChild:!0,children:(0,s.jsxs)("div",{className:"px-6 pb-6 text-center",children:[(0,s.jsx)("p",{className:"text-apple-body text-muted-foreground mb-6",children:"Get started by connecting to your AI models"}),(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsx)(d.$,{variant:"default",className:"bg-primary text-primary-foreground hover:bg-primary/90 rounded-lg px-6 py-2 text-apple-callout font-medium",onClick:()=>a(!0),"aria-label":"Open Settings",children:"Open Settings"})})]})})]})}),_=({children:e})=>(0,s.jsx)("div",{className:(0,l.cn)("inline-block whitespace-nowrap overflow-hidden text-ellipsis w-full max-w-xs","bg-transparent text-[var(--text)]","rounded-md py-0.5","font-['poppins',_sans-serif] text-md text-center font-medium"),children:e}),R=({isOpen:e,onOpenChange:t,config:a,updateConfig:r})=>{const[o,i]=(0,n.useState)(a?.userName||""),[u,m]=(0,n.useState)(a?.userProfile||"");return(0,n.useEffect)((()=>{e&&(i(a?.userName||""),m(a?.userProfile||""))}),[e,a?.userName,a?.userProfile]),(0,s.jsx)(M.lG,{open:e,onOpenChange:t,children:(0,s.jsxs)(M.Cf,{variant:"themedPanel",className:"max-w-xs",children:[(0,s.jsxs)(M.c7,{className:"px-6 py-4 border-b border-[var(--text)]/10",children:[(0,s.jsx)(M.L3,{className:"text-lg font-semibold text-[var(--text)]",children:"Edit Profile"}),(0,s.jsx)(M.rr,{className:"text-sm text-[var(--text)] opacity-80",children:"Set your display name and profile information. (For chat and export purposes)"})]}),(0,s.jsxs)("div",{className:"px-6 py-5 space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-1.5",children:[(0,s.jsx)(A.J,{htmlFor:"username",className:"text-sm font-medium text-[var(--text)] opacity-90",children:"Username"}),(0,s.jsx)(w.p,{id:"username",value:o,onChange:e=>i(e.target.value),className:(0,l.cn)("focus:border-[var(--active)] focus:ring-1 focus:ring-[var(--active)]","hover:border-[var(--active)] hover:brightness-98")})]}),(0,s.jsxs)("div",{className:"space-y-1.5",children:[(0,s.jsx)(A.J,{htmlFor:"userprofile",className:"text-sm font-medium text-[var(--text)] opacity-90",children:"User Profile"}),(0,s.jsx)(w.p,{id:"userprofile",value:u,onChange:e=>m(e.target.value),className:(0,l.cn)("focus:border-[var(--active)] focus:ring-1 focus:ring-[var(--active)]","hover:border-[var(--active)] hover:brightness-98")})]})]}),(0,s.jsxs)(M.Es,{className:"px-6 py-4 border-t border-[var(--text)]/10",children:[(0,s.jsx)(d.$,{variant:"outline-subtle",size:"sm",onClick:()=>t(!1),children:"Cancel"}),(0,s.jsx)(d.$,{variant:"active-bordered",size:"sm",onClick:()=>{r({userName:o,userProfile:u}),t(!1),c.oR.success("Profile updated!")},children:"Save Changes"})]})]})})},O=({chatTitle:e,settingsMode:t,setSettingsMode:a,historyMode:u,setHistoryMode:m,noteSystemMode:h,setNoteSystemMode:p,deleteAll:g,reset:f,downloadImage:x,downloadJson:v,downloadText:b,downloadMarkdown:y,chatMode:w,chatStatus:N,onAddNewNoteRequest:C})=>{const{config:k,updateConfig:M}=(0,i.UK)(),[A,O]=(0,n.useState)(!1),I=k?.persona||"default",D=(S.z[I]||S.z.default,e&&!t&&!u&&!h),[U,F]=(0,n.useState)(!1),q=t||u||h,W=q?"Back to Chat":"",B="z-50 min-w-[6rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md animate-in data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",G="flex cursor-default select-none items-center rounded-sm px-2 py-1 text-sm outline-none transition-colors focus:bg-accent focus:text-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50";return(0,s.jsx)(j.Bc,{delayDuration:500,children:(0,s.jsxs)("div",{className:(0,l.cn)("bg-background/95 backdrop-blur-sm text-foreground","border-b border-border","sticky top-0 z-10"),children:[(0,s.jsxs)("div",{className:"flex items-center h-auto py-3 px-4",children:[(0,s.jsx)("div",{className:"flex justify-start items-center min-h-10 w-12",children:q&&(0,s.jsxs)(j.m_,{children:[(0,s.jsx)(j.k$,{asChild:!0,children:(0,s.jsx)(d.$,{"aria-label":W,variant:"ghost",size:"sm",className:"text-foreground hover:bg-secondary rounded-lg p-2 h-8 w-8 flex items-center justify-center",onClick:()=>{q&&(a(!1),m(!1),p(!1))},children:(0,s.jsx)(r.yGN,{size:"18px"})})}),(0,s.jsx)(j.ZI,{side:"bottom",className:"bg-popover text-popover-foreground border border-border",children:W})]})}),(0,s.jsxs)("div",{className:"flex-grow flex justify-center items-center overflow-hidden px-4",children:[D&&(0,s.jsx)("p",{className:"text-apple-headline text-foreground whitespace-nowrap overflow-hidden text-ellipsis text-center",children:e}),!D&&!u&&!t&&!h&&(0,s.jsx)(_,{className:"bg-secondary text-secondary-foreground border-border",children:k?.selectedModel||"No Model Selected"}),t&&(0,s.jsx)("div",{className:"flex items-center justify-center",children:(0,s.jsx)("p",{className:"text-apple-title3 text-foreground",children:"Settings"})}),u&&(0,s.jsx)("div",{className:"flex items-center justify-center",children:(0,s.jsx)("p",{className:"text-apple-title3 text-foreground",children:"Chat History"})}),h&&(0,s.jsx)("div",{className:"flex items-center justify-center",children:(0,s.jsx)("p",{className:"text-apple-title3 text-foreground",children:"Note System"})})]}),(0,s.jsxs)("div",{className:"flex justify-end items-center min-h-10 gap-2",children:[!t&&!u&&!h&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(j.m_,{children:[(0,s.jsx)(j.k$,{asChild:!0,children:(0,s.jsx)(d.$,{"aria-label":"Reset Chat",variant:"ghost",size:"sm",className:"text-foreground hover:bg-secondary rounded-lg p-2 h-8 w-8 flex items-center justify-center group",onClick:f,children:(0,s.jsx)(o.yPB,{size:"16px",className:"transition-transform duration-300 rotate-0 group-hover:rotate-180"})})}),(0,s.jsx)(j.ZI,{side:"bottom",className:"bg-popover text-popover-foreground border border-border",children:"Reset Chat"})]}),(0,s.jsxs)(j.m_,{children:[(0,s.jsx)(j.k$,{asChild:!0,children:(0,s.jsx)(d.$,{"aria-label":"Settings",variant:"ghost",size:"sm",className:"text-foreground hover:bg-secondary rounded-lg p-2 h-8 w-8 flex items-center justify-center",onClick:()=>{a(!0)},children:(0,s.jsx)(r.VSk,{size:"16px"})})}),(0,s.jsx)(j.ZI,{side:"bottom",className:"bg-popover text-popover-foreground border border-border",children:"Settings"})]}),(0,s.jsxs)(E.bL,{children:[(0,s.jsxs)(j.m_,{children:[(0,s.jsx)(j.k$,{asChild:!0,children:(0,s.jsx)(E.l9,{asChild:!0,children:(0,s.jsx)(d.$,{"aria-label":"Share Options",variant:"ghost",size:"sm",className:"text-[var(--text)] rounded-md",children:(0,s.jsx)(r.pdY,{size:"18px"})})})}),(0,s.jsx)(j.ZI,{side:"bottom",className:"bg-[var(--active)]/50 text-[var(--text)] border-[var(--text)]",children:"Share Options"})]}),(0,s.jsx)(E.ZL,{children:(0,s.jsxs)(E.UC,{className:(0,l.cn)(B,"bg-[var(--bg)] text-[var(--text)] border-[var(--text)]/20 shadow-xl"),sideOffset:5,align:"end",children:[(0,s.jsxs)(E.q7,{className:(0,l.cn)(G,"hover:bg-[var(--active)]/30 focus:bg-[var(--active)]/30 cursor-pointer"),onSelect:()=>O(!0),children:[(0,s.jsx)(T.uSr,{className:"mr-auto h-4 w-4"}),"Edit Profile"]}),(0,s.jsx)(E.wv,{className:(0,l.cn)("-mx-1 my-1 h-px bg-muted","bg-[var(--text)]/10")}),(0,s.jsxs)(E.Pb,{children:[(0,s.jsxs)(E.ZP,{className:(0,l.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent","hover:bg-[var(--active)]/30 focus:bg-[var(--active)]/30 cursor-pointer"),children:[(0,s.jsx)(r.irw,{className:"mr-auto h-4 w-4"}),"Export Chat"]}),(0,s.jsx)(E.ZL,{children:(0,s.jsxs)(E.G5,{className:(0,l.cn)(B,"bg-[var(--bg)] text-[var(--text)] border-[var(--text)]/20 shadow-lg"),sideOffset:2,alignOffset:-5,children:[(0,s.jsxs)(E.q7,{className:(0,l.cn)(G,"hover:bg-[var(--active)]/30 focus:bg-[var(--active)]/30 cursor-pointer"),onSelect:y,children:[(0,s.jsx)(P.nR3,{className:"mr-auto h-4 w-4"}),".md"]}),(0,s.jsxs)(E.q7,{className:(0,l.cn)(G,"hover:bg-[var(--active)]/30 focus:bg-[var(--active)]/30 cursor-pointer"),onSelect:b,children:[(0,s.jsx)(T.mup,{className:"mr-auto h-4 w-4"}),".txt"]}),(0,s.jsxs)(E.q7,{className:(0,l.cn)(G,"hover:bg-[var(--active)]/30 focus:bg-[var(--active)]/30 cursor-pointer"),onSelect:v,children:[(0,s.jsx)(o.dG_,{className:"mr-auto h-4 w-4"}),".json"]}),(0,s.jsxs)(E.q7,{className:(0,l.cn)(G,"hover:bg-[var(--active)]/30 focus:bg-[var(--active)]/30 cursor-pointer"),onSelect:x,children:[(0,s.jsx)(T.Af8,{className:"mr-auto h-4 w-4"}),".png"]})]})})]})]})})]})]}),u&&(0,s.jsxs)(j.m_,{children:[(0,s.jsx)(j.k$,{asChild:!0,children:(0,s.jsx)(d.$,{"aria-label":"Delete All History",variant:"ghost",size:"sm",className:"text-[var(--text)] rounded-md",onClick:()=>{c.oR.custom((e=>(0,s.jsxs)("div",{className:(0,l.cn)("bg-[var(--bg)] text-[var(--text)] border border-[var(--text)]","p-4 rounded-lg shadow-xl max-w-sm w-full","flex flex-col space-y-3"),children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-[var(--text)]",children:"Confirm Deletion"}),(0,s.jsx)("p",{className:"text-sm text-[var(--text)] opacity-90",children:"Are you sure you want to delete all chat history? This action cannot be undone."}),(0,s.jsxs)("div",{className:"flex justify-end space-x-3 pt-2",children:[(0,s.jsx)(d.$,{variant:"outline",size:"sm",className:(0,l.cn)("bg-transparent text-[var(--text)] border-[var(--text)]","hover:bg-[var(--active)]/30 focus:ring-1 focus:ring-[var(--active)]"),onClick:()=>c.oR.dismiss(e.id),children:"Cancel"}),(0,s.jsx)(d.$,{variant:"destructive",size:"sm",className:(0,l.cn)("focus:ring-1 focus:ring-red-400 focus:ring-offset-1 focus:ring-offset-[var(--bg)]"),onClick:async()=>{try{"function"==typeof g?await g():(console.error("Header: deleteAll prop is not a function or undefined.",g),c.oR.error("Failed to delete history: Operation not available."))}catch(e){console.error("Error during deleteAll execution from header:",e),c.oR.error("An error occurred while deleting history.")}finally{c.oR.dismiss(e.id)}},children:"Delete All"})]})]})),{duration:1/0,position:"top-center"})},children:(0,s.jsx)(r.IXo,{size:"18px"})})}),(0,s.jsx)(j.ZI,{side:"bottom",className:"bg-[var(--active)]/50 text-[var(--text)] border-[var(--text)]",children:"Delete All"})]}),h&&C&&(0,s.jsxs)(j.m_,{children:[(0,s.jsx)(j.k$,{asChild:!0,children:(0,s.jsxs)(d.$,{"aria-label":"Add New Note",variant:"ghost",size:"sm",className:"text-[var(--text)] rounded-md",onClick:C,children:[(0,s.jsx)(z.BlJ,{size:"18px"})," "]})}),(0,s.jsx)(j.ZI,{side:"bottom",className:"bg-[var(--active)]/50 text-[var(--text)] border-[var(--text)]",children:"Add New Note"})]})]})]}),(!k?.models||0===k.models.length)&&!t&&!u&&!h&&(0,s.jsx)(L,{isOpen:!0,setSettingsMode:a,onClose:()=>{}}),(0,s.jsx)($,{isOpen:U,onOpenChange:e=>{F(e)},config:k,updateConfig:M,setSettingsMode:a,setHistoryMode:m,setNoteSystemMode:p}),(0,s.jsx)(R,{isOpen:A,onOpenChange:O,config:k,updateConfig:M})]})})}},3190:(e,t,a)=>{a.d(t,{g:()=>d});var s=a(38),n=a(9448),r=a(7346),o=a(3207),i=a(5886);const l={...a(6108).z2,...i.z2},c=((0,o.nK)(l),r.P,(0,s.N0)(),n.logger,[(0,o.nK)(l),r.P,(0,s.N0)(),n.logger]);(0,o.nK)(l),n.logger;const d=e=>{const t=new o.il({channelName:e});return(0,o.Tw)(t,...c),t}},3842:(e,t,a)=>{a.d(t,{w:()=>K});var s=a(4848),n=a(6540),r=a(1584),o=a(5107),i=a(5284);function l({...e}){return(0,s.jsx)(r.bL,{"data-slot":"accordion",...e})}function c({className:e,...t}){return(0,s.jsx)(r.q7,{"data-slot":"accordion-item",className:(0,i.cn)("border-b last:border-b-0","bg-[var(--input-background)]","shadow-md","rounded-xl","border-[var(--text)]/10",e),...t})}function d({className:e,children:t,...a}){return(0,s.jsx)(r.Y9,{className:"flex",children:(0,s.jsxs)(r.l9,{"data-slot":"accordion-trigger",className:(0,i.cn)("focus-visible:border-ring focus-visible:ring-ring/50 flex flex-1 items-start justify-between gap-4 rounded-md py-4 text-left text-sm font-medium transition-all outline-none hover:underline focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 [&[data-state=open]>svg]:rotate-180",e),...a,children:[t,(0,s.jsx)(o.A,{className:"text-muted-foreground pointer-events-none size-4 shrink-0 translate-y-0.5 transition-transform duration-200"})]})})}function u({className:e,children:t,...a}){return(0,s.jsx)(r.UC,{"data-slot":"accordion-content",className:"data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down overflow-hidden text-sm",...a,children:(0,s.jsx)("div",{className:(0,i.cn)("pt-0 pb-4",e),children:t})})}var m=a(6948),h=a(888),p=a(3),g=a(2090),f=a(6532);const x=()=>{const{config:e,updateConfig:t}=(0,m.UK)(),[a,r]=(0,n.useState)(e?.ollamaUrl||"http://localhost:11434"),[o,l]=(0,n.useState)(!1),c=()=>{l(!0),h.Ay.dismiss(),h.Ay.loading("Connecting to Ollama..."),fetch(`${a}/api/tags`).then((e=>e.ok?e.json():e.json().then((t=>{throw new Error(t?.error||`Connection failed: ${e.status} ${e.statusText}`)})).catch((()=>{throw new Error(`Connection failed: ${e.status} ${e.statusText}`)})))).then((s=>{Array.isArray(s.models)?(t({ollamaConnected:!0,ollamaUrl:a,ollamaError:void 0,models:(e?.models||[]).filter((e=>"ollama_generic"!==e.id)).concat([{id:"ollama_generic",host:"ollama",active:!0,name:"Ollama Model"}]),selectedModel:"ollama_generic"}),h.Ay.dismiss(),h.Ay.success("Connected to ollama")):s?.error?(t({ollamaError:s.error,ollamaConnected:!1}),h.Ay.dismiss(),h.Ay.error("string"==typeof s.error?s.error:"Ollama connection error")):(t({ollamaError:"Unexpected response from Ollama",ollamaConnected:!1}),h.Ay.dismiss(),h.Ay.error("Unexpected response from Ollama"))})).catch((e=>{h.Ay.dismiss(),h.Ay.error(e.message||"Failed to connect to Ollama"),t({ollamaError:e.message,ollamaConnected:!1})})).finally((()=>{l(!1)}))},d=e?.ollamaConnected;return(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(f.p,{id:"ollama-url-input",value:a,onChange:e=>r(e.target.value),placeholder:"http://localhost:11434",className:"pr-8",disabled:o}),!d&&(0,s.jsx)(g.$,{onClick:c,variant:"connect",size:"sm",disabled:o,children:o?"...":"Connect"}),d&&(0,s.jsx)(g.$,{variant:"ghost",size:"sm","aria-label":"Connected to Ollama",className:(0,i.cn)("w-8 rounded-md text-[var(--success)]"),disabled:o,onClick:c,children:(0,s.jsx)(p.YrT,{className:"h-5 w-5"})})]})};var v=a(9018),b=a(8698);const y=()=>{const{config:e,updateConfig:t}=(0,m.UK)(),{fetchAllModels:a}=(0,b.N)(),[r,o]=(0,n.useState)(!1),[l,c]=(0,n.useState)(null),d=async()=>{if(e?.ollamaUrl&&e?.ollamaConnected){o(!0),c(null);try{await a(),h.Ay.success("Models refreshed successfully")}catch(e){const t=e instanceof Error?e.message:"Failed to refresh models";c(t),h.Ay.error(t),console.error("Error refreshing models:",e)}finally{o(!1)}}else c("Ollama not connected")};(0,n.useEffect)((()=>{e?.ollamaConnected?d():c(null)}),[e?.ollamaConnected,e?.ollamaUrl]);const u=e?.ollamaConnected,f=e?.selectedModel,x=e?.models?.filter((e=>"ollama"===e.host))||[];return u?l?(0,s.jsx)("div",{className:"space-y-3",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center text-red-500",children:[(0,s.jsx)(p.y3G,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{className:"text-sm",children:l})]}),(0,s.jsx)(g.$,{onClick:d,variant:"ghost",size:"sm",disabled:r,className:"h-8 w-8 p-0",children:(0,s.jsx)(p.jTZ,{className:(0,i.cn)("h-4 w-4",r&&"animate-spin")})})]})}):0!==x.length||r?(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsxs)(v.l6,{value:f||"",onValueChange:e=>{t({selectedModel:e}),h.Ay.success(`Selected model: ${e}`)},disabled:r||0===x.length,children:[(0,s.jsx)(v.bq,{variant:"settingsPanel",className:(0,i.cn)("w-full",r&&"opacity-50"),children:(0,s.jsx)(v.yv,{placeholder:r?"Loading models...":"Select a model..."})}),(0,s.jsx)(v.gC,{variant:"settingsPanel",children:x.map((e=>(0,s.jsx)(v.eb,{value:e.id,focusVariant:"activeTheme",className:"text-[var(--text)]",children:(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsx)("span",{className:"font-medium",children:e.name||e.id}),e.context_length&&(0,s.jsxs)("span",{className:"text-xs text-[var(--text)]/60",children:["Context: ",e.context_length]})]})},e.id)))})]})}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[f&&x.some((e=>e.id===f))&&(0,s.jsx)("div",{className:"flex items-center text-[var(--success)]",children:(0,s.jsx)(p.YrT,{className:"h-4 w-4"})}),(0,s.jsx)(g.$,{onClick:d,variant:"ghost",size:"sm",disabled:r,className:"h-8 w-8 p-0 hover:bg-[var(--text)]/10",title:"Refresh models",children:(0,s.jsx)(p.jTZ,{className:(0,i.cn)("h-4 w-4",r&&"animate-spin")})})]})]}):(0,s.jsxs)("div",{className:"flex items-center justify-between py-2",children:[(0,s.jsxs)("div",{className:"flex items-center text-[var(--text)]/60",children:[(0,s.jsx)(p.y3G,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{className:"text-sm",children:"No models available"})]}),(0,s.jsx)(g.$,{onClick:d,variant:"ghost",size:"sm",disabled:r,className:"h-8 w-8 p-0",children:(0,s.jsx)(p.jTZ,{className:(0,i.cn)("h-4 w-4",r&&"animate-spin")})})]}):(0,s.jsxs)("div",{className:"flex items-center justify-center py-4 text-[var(--text)]/60",children:[(0,s.jsx)(p.y3G,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{className:"text-sm",children:"Connect to Ollama first"})]})},w=({text:e="",widget:t=(0,s.jsx)(s.Fragment,{}),icon:a=""})=>(0,s.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[a&&(0,s.jsx)("span",{className:(0,i.cn)("text-foreground","text-xl","leading-none","mr-3"),children:a}),(0,s.jsx)("span",{className:(0,i.cn)("text-foreground","opacity-90","font-['Space_Mono',_monospace]","text-base","font-bold"),children:e})]}),t&&(0,s.jsx)("div",{className:"ml-2",children:t})]}),j=({title:e,Component:t})=>(0,s.jsxs)("div",{className:"px-4 py-3 border-b border-[var(--text)]/10 last:border-b-0",children:[(0,s.jsx)("div",{className:"flex items-center justify-between mb-2",children:(0,s.jsx)("h4",{className:"text-base font-medium capitalize text-foreground",children:e})}),(0,s.jsx)(t,{})]}),N=()=>(0,s.jsxs)(c,{value:"connect",className:(0,i.cn)("bg-[var(--input-background)]","border-[var(--text)]/10","rounded-xl","shadow-md","transition-all duration-150 ease-in-out","hover:border-[var(--active)] hover:brightness-105","overflow-hidden"),children:[(0,s.jsx)(d,{className:(0,i.cn)("flex items-center justify-between w-full px-3 py-2 hover:no-underline","text-[var(--text)] font-medium","hover:brightness-95"),children:(0,s.jsx)(w,{icon:"♾️",text:"API Access"})}),(0,s.jsxs)(u,{className:"p-0 text-[var(--text)]",children:[(0,s.jsx)(j,{Component:x,title:"Ollama"}),(0,s.jsx)(j,{Component:y,title:"Model Selection"})]})]});var C=a(803),S=a(2732);const k=(0,S.F)("relative flex w-full touch-none select-none items-center data-[disabled]:opacity-50 data-[orientation=vertical]:h-full data-[orientation=vertical]:min-h-44 data-[orientation=vertical]:w-auto data-[orientation=vertical]:flex-col",{variants:{variant:{default:["[&>span[data-slot=slider-track]]:bg-secondary","[&>span[data-slot=slider-track]>span[data-slot=slider-range]]:bg-primary","[&>button[data-slot=slider-thumb]]:bg-background","[&>button[data-slot=slider-thumb]]:border-primary","[&>button[data-slot=slider-thumb]]:ring-offset-background","[&>button[data-slot=slider-thumb]]:focus-visible:ring-ring"],themed:["[&>span[data-slot=slider-track]]:bg-[var(--text)]/10","[&>span[data-slot=slider-track]>span[data-slot=slider-range]]:bg-[var(--active)]","[&>button[data-slot=slider-thumb]]:bg-[var(--active)]","[&>button[data-slot=slider-thumb]]:border-[var(--text)]/50","[&>button[data-slot=slider-thumb]]:ring-offset-[var(--bg)]","[&>button[data-slot=slider-thumb]]:focus-visible:ring-[var(--active)]"]}},defaultVariants:{variant:"default"}});function $({className:e,variant:t,defaultValue:a,value:r,min:o=0,max:l=100,...c}){const d=n.useMemo((()=>Array.isArray(r)?r:Array.isArray(a)?a:[o,l]),[r,a,o]);return(0,s.jsxs)(C.bL,{"data-slot":"slider",defaultValue:a,value:r,min:o,max:l,className:(0,i.cn)(k({variant:t,className:e})),...c,children:[(0,s.jsx)(C.CC,{"data-slot":"slider-track",className:(0,i.cn)("relative h-1.5 w-full grow overflow-hidden rounded-full","data-[orientation=vertical]:h-full data-[orientation=vertical]:w-1.5"),children:(0,s.jsx)(C.Q6,{"data-slot":"slider-range",className:(0,i.cn)("absolute h-full","data-[orientation=vertical]:w-full")})}),(d.length>0?d:[o]).map(((e,t)=>(0,s.jsx)(C.zi,{"data-slot":"slider-thumb",className:(0,i.cn)("block h-4 w-4 bg-white rounded-full border border-primary/50 shadow-sm transition-colors focus-visible:ring-1 disabled:pointer-events-none disabled:opacity-50")},t)))]})}const M=({size:e,updateConfig:t})=>(0,s.jsxs)("div",{className:"w-full",children:[(0,s.jsxs)("p",{className:"text-[var(--text)] text-base font-medium pb-6 text-left",children:["Char Limit:"," ",(0,s.jsx)("span",{className:"font-normal",children:128===e?"inf":`${e}k`})]}),(0,s.jsx)($,{defaultValue:[e],max:128,min:1,step:1,variant:"themed",onValueChange:e=>t({contextLimit:e[0]})})]}),A=()=>{const{config:e,updateConfig:t}=(0,m.UK)(),a=e?.contextLimit||1;return(0,s.jsxs)(c,{value:"page-context",className:(0,i.cn)("bg-[var(--input-background)] border-[var(--text)]/10 rounded-xl shadow-md","overflow-hidden","transition-all duration-150 ease-in-out","hover:border-[var(--active)] hover:brightness-105"),children:[(0,s.jsx)(d,{className:(0,i.cn)("flex items-center justify-between w-full px-3 py-2 hover:no-underline","text-[var(--text)] font-medium","hover:brightness-95"),children:(0,s.jsx)(w,{icon:"📃",text:"Page Context"})}),(0,s.jsx)(u,{className:"px-3 pb-4 pt-2 text-[var(--text)]",children:(0,s.jsx)(M,{size:a,updateConfig:t})})]})};var E=a(5634);const T=()=>{const{config:e,updateConfig:t}=(0,m.UK)(),a=e=>a=>{const s=Array.isArray(a)?a[0]:a;t({[e]:s})},n=e.temperature??.7,r=e.maxTokens??32048,o=e.topP??.95,l=e.presencepenalty??0;return(0,s.jsxs)(c,{value:"model-params",className:(0,i.cn)("bg-[var(--input-background)] border-[var(--text)]/10 rounded-xl shadow-md","transition-all duration-150 ease-in-out","hover:border-[var(--active)] hover:brightness-105"),children:[(0,s.jsx)(d,{className:(0,i.cn)("flex items-center justify-between w-full px-3 py-2 hover:no-underline","text-[var(--text)] font-medium","hover:brightness-95"),children:(0,s.jsx)(w,{icon:"⚙️",text:"Model Config"})}),(0,s.jsx)(u,{className:"px-3 pb-4 pt-2 text-[var(--text)]",children:(0,s.jsxs)("div",{className:"flex flex-col gap-6",children:[(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)(E.J,{htmlFor:"temperature",className:"text-base font-medium text-foreground",children:["Temperature (",n.toFixed(2),")"]}),(0,s.jsx)($,{id:"temperature",min:0,max:2,step:.01,value:[n],onValueChange:a("temperature"),variant:"themed"})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)(E.J,{htmlFor:"maxTokens",className:"text-base font-medium text-foreground",children:["Max Tokens (",r,")"]}),(0,s.jsx)(f.p,{id:"maxTokens",type:"number",value:r,min:1,max:128e4,onChange:e=>a("maxTokens")(parseInt(e.target.value,10)||0),className:(0,i.cn)("hide-number-spinners")})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)(E.J,{htmlFor:"topP",className:"text-base font-medium text-foreground",children:["Top P (",o.toFixed(2),")"]}),(0,s.jsx)($,{id:"topP",min:0,max:1,step:.01,value:[o],onValueChange:a("topP"),variant:"themed"})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)(E.J,{htmlFor:"presencepenalty",className:"text-base font-medium text-foreground",children:["Presence Penalty (",l.toFixed(2),")"]}),(0,s.jsx)($,{id:"presencepenalty",min:-2,max:2,step:.01,value:[l],onValueChange:a("presencepenalty"),variant:"themed"})]})]})})]})};var z=a(7086),P=a(3732),L=a(9696);const _=({hasChange:e,onSave:t,onSaveAs:a,onCancel:n})=>e?(0,s.jsxs)("div",{className:"flex mt-4 space-x-2 justify-end w-full",children:[(0,s.jsx)(g.$,{variant:"active-bordered",size:"sm",onClick:t,children:"Save"}),(0,s.jsx)(g.$,{variant:"active-bordered",size:"sm",onClick:a,children:"Save As..."}),(0,s.jsx)(g.$,{variant:"outline-subtle",size:"sm",onClick:n,children:"Cancel"})]}):null,R=({isOpen:e,onOpenChange:t,personaPrompt:a,personas:r,updateConfig:o,onModalClose:l})=>{const[c,d]=(0,n.useState)("");return(0,s.jsx)(z.lG,{open:e,onOpenChange:t,children:(0,s.jsxs)(z.Cf,{className:(0,i.cn)("sm:max-w-[425px]","bg-[var(--bg)]"),onCloseAutoFocus:e=>e.preventDefault(),children:[(0,s.jsx)(z.c7,{children:(0,s.jsx)(z.L3,{className:"text-[var(--text)]",children:"Create New Persona"})}),(0,s.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,s.jsx)(E.J,{htmlFor:"persona-name",className:"text-base font-medium text-foreground sr-only",children:"Persona Name"}),(0,s.jsx)(f.p,{id:"persona-name",placeholder:"Enter persona name",value:c,onChange:e=>d(e.target.value),className:(0,i.cn)("focus:border-[var(--active)] focus:ring-1 focus:ring-[var(--active)]","hover:border-[var(--active)] hover:brightness-98")})]}),(0,s.jsxs)(z.Es,{className:"sm:justify-end",children:[(0,s.jsx)(g.$,{type:"button",variant:"outline-subtle",size:"sm",onClick:l,children:" Cancel "}),(0,s.jsx)(g.$,{type:"button",variant:"active-bordered",size:"sm",className:(0,i.cn)(),disabled:!c.trim(),onClick:()=>{c.trim()&&(o({personas:{...r,[c.trim()]:a},persona:c.trim()}),d(""),l())},children:" Create "})]})]})})},O=({isOpen:e,onOpenChange:t,persona:a,personas:n,updateConfig:r,onModalClose:o})=>(0,s.jsx)(z.lG,{open:e,onOpenChange:t,children:(0,s.jsxs)(z.Cf,{className:(0,i.cn)("sm:max-w-[425px]","bg-[var(--bg)]","border","text-[var(--text)]"),children:[(0,s.jsxs)(z.c7,{children:[(0,s.jsxs)(z.L3,{className:"text-[var(--text)]",children:['Delete "',a,'"']}),(0,s.jsx)(z.rr,{className:"text-[var(--text)]/80 pt-2",children:"Are you sure you want to delete this persona? This action cannot be undone."})]}),(0,s.jsxs)(z.Es,{className:"sm:justify-end pt-4",children:[(0,s.jsx)(g.$,{type:"button",variant:"outline-subtle",size:"sm",onClick:o,children:" Cancel "}),(0,s.jsx)(g.$,{type:"button",variant:"destructive",size:"sm",onClick:()=>{const e={...n};delete e[a];const t=Object.keys(e);r({personas:e,persona:t.length>0?t[0]:"Scholar"}),o()},children:" Delete "})]})]})}),I=({personas:e,persona:t,updateConfig:a})=>(0,s.jsxs)(v.l6,{value:t,onValueChange:e=>a({persona:e}),children:[(0,s.jsx)(v.bq,{variant:"settings",className:(0,i.cn)("flex w-full","data-[placeholder]:text-muted-foreground"),children:(0,s.jsx)(v.yv,{placeholder:"Select persona"})}),(0,s.jsx)(v.gC,{variant:"settingsPanel",className:(0,i.cn)(),children:Object.keys(e).map((e=>(0,s.jsxs)(v.eb,{value:e,focusVariant:"activeTheme",children:[" ",e," "]},e)))})]}),D=({personaPrompt:e,setPersonaPrompt:t,isEditing:a,setIsEditing:n})=>{const r={onFocus:e=>{a||n(!0)}};return(0,s.jsx)(L.T,{autosize:!0,minRows:3,maxRows:8,value:e,onChange:e=>{a||n(!0),t(e.target.value)},readOnly:!a,...r,"data-slot":"textarea-default",placeholder:"Define the persona's characteristics and instructions here...",className:(0,i.cn)("w-full min-h-[80px] border border-[var(--text)]/10 px-3 py-2 text-sm ring-offset-[var(--bg)] placeholder:text-[var(--muted-foreground)] rounded-[12px]","text-[var(--text)]","no-scrollbar","focus-visible:outline-none focus-visible:ring-0 focus-visible:box-shadow-[inset_0_0_0_1px_rgba(255,255,255,0.1),_0_0_8px_rgba(168,123,255,0.3)]",a?"hover:border-[var(--active)] focus:border-[var(--active)]":"opacity-75 cursor-default")})},U=()=>{const{config:e,updateConfig:t}=(0,m.UK)(),[a,r]=(0,n.useState)(!1),[o,l]=(0,n.useState)(!1),[h,p]=(0,n.useState)(!1),f=e?.personas||{Scholar:"You are The Scholar, an analytical academic researcher specializing in web search analysis."},x=e?.persona||"Scholar",v=f?.[x]??f?.Scholar??"You are The Scholar, an analytical academic researcher specializing in web search analysis.",[b,y]=(0,n.useState)(v),j=h&&b!==v;return(0,n.useEffect)((()=>{y(f?.[x]??f?.Scholar??""),p(!1)}),[x,JSON.stringify(f)]),(0,s.jsxs)(c,{value:"persona",className:(0,i.cn)("bg-[var(--input-background)] border-[var(--text)]/10 rounded-xl shadow-md","transition-all duration-150 ease-in-out","hover:border-[var(--active)] hover:brightness-105"),children:[(0,s.jsx)(d,{className:(0,i.cn)("flex items-center justify-between w-full px-3 py-2 hover:no-underline","text-[var(--text)] font-medium","hover:brightness-95"),children:(0,s.jsx)(w,{icon:"🥷",text:"Persona"})}),(0,s.jsx)(u,{className:"px-3 pb-4 pt-2 text-[var(--text)]",children:(0,s.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(I,{persona:x,personas:f,updateConfig:t}),(0,s.jsxs)(g.$,{variant:"ghost",size:"sm","aria-label":"Add new persona",className:(0,i.cn)("text-[var(--text)] p-1.5 rounded-md","focus-visible:ring-1 focus-visible:ring-[var(--active)] focus-visible:ring-offset-1 focus-visible:ring-offset-[var(--bg)]"),onClick:()=>{y(""),p(!0),r(!0)},children:[" ",(0,s.jsx)(P.YHj,{className:"h-5 w-5"})," "]}),Object.keys(f).length>1&&(0,s.jsxs)(g.$,{variant:"ghost",size:"sm","aria-label":"Delete current persona",className:(0,i.cn)("text-[var(--text)] hover:text-[var(--error)] hover:bg-[var(--error)]/10 p-1.5 rounded-md","focus-visible:ring-1 focus-visible:ring-[var(--error)] focus-visible:ring-offset-1 focus-visible:ring-offset-[var(--bg)]"),onClick:()=>l(!0),children:[" ",(0,s.jsx)(P.dW_,{className:"h-5 w-5"})," "]})]}),(0,s.jsx)(D,{personaPrompt:b,setPersonaPrompt:y,isEditing:h,setIsEditing:p}),(0,s.jsx)(_,{hasChange:j,onSave:()=>{t({personas:{...f,[x]:b}}),p(!1)},onSaveAs:()=>{r(!0)},onCancel:()=>{y(v),p(!1)}})]})}),(0,s.jsx)(R,{isOpen:a,onOpenChange:e=>{r(e),e||(y(v),p(!1))},personaPrompt:b,personas:f,updateConfig:t,onModalClose:()=>r(!1)}),(0,s.jsx)(O,{isOpen:o,onOpenChange:l,persona:x,personas:f,updateConfig:t,onModalClose:()=>l(!1)})]})};var F=a(9451),q=a(8309);function W({className:e,...t}){return(0,s.jsx)(F.bL,{"data-slot":"radio-group",className:(0,i.cn)("grid gap-3",e),...t})}const B=(0,S.F)("aspect-square size-4 shrink-0 rounded-full border shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:["border-input dark:bg-input/30 text-primary focus-visible:border-ring focus-visible:ring-ring/50"],themed:["border-[var(--text)] text-[var(--active)]","focus-visible:ring-1 focus-visible:ring-[var(--active)] focus-visible:ring-offset-0 focus-visible:border-[var(--active)]","data-[state=checked]:border-[var(--active)]"]}},defaultVariants:{variant:"default"}});function G({className:e,variant:t,...a}){return(0,s.jsx)(F.q7,{"data-slot":"radio-group-item",className:(0,i.cn)(B({variant:t,className:e})),...a,children:(0,s.jsx)(F.C1,{"data-slot":"radio-group-indicator",className:"relative flex items-center justify-center",children:(0,s.jsx)(q.A,{className:(0,i.cn)("absolute top-1/2 left-1/2 size-2 -translate-x-1/2 -translate-y-1/2","themed"===t?"fill-[var(--active)]":"fill-primary")})})})}const V=({webMode:e,updateConfig:t})=>(0,s.jsx)(W,{value:e,onValueChange:e=>t({webMode:e}),className:"w-1/2 space-y-3",children:["Google"].map((e=>(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(G,{value:e,id:`webMode-${e}`,variant:"themed"}),(0,s.jsx)(E.J,{htmlFor:`webMode-${e}`,className:"text-[var(--text)] text-base font-medium cursor-pointer",children:e})]},e)))}),H=({config:e,updateConfig:t})=>{const a=e?.webLimit??16,n=e?.serpMaxLinksToVisit??3;return(0,s.jsxs)("div",{className:"w-full space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{className:"text-[var(--text)] text-base font-medium pb-2 text-left",children:["Max Links to Visit: ",(0,s.jsx)("span",{className:"font-normal",children:n})]}),(0,s.jsx)($,{value:[n],max:10,min:1,step:1,variant:"themed",onValueChange:e=>t({serpMaxLinksToVisit:e[0]})}),(0,s.jsx)("p",{className:"text-[var(--text)]/70 text-xs pt-1",children:"Number of search result links to fetch."})]}),(0,s.jsxs)("div",{className:"pt-2",children:[(0,s.jsxs)("p",{className:"text-[var(--text)] text-base font-medium pb-2 text-left",children:["Content Char Limit:"," ",(0,s.jsx)("span",{className:"font-normal",children:128===a?"Unlimited (Full)":`${a}k`})]}),(0,s.jsx)($,{value:[a],max:128,min:1,step:1,variant:"themed",onValueChange:e=>t({webLimit:e[0]})}),(0,s.jsx)("p",{className:"text-[var(--text)]/70 text-xs pt-1",children:"Max characters (in thousands) of content to use. 128k for 'Unlimited'."})]})]})},Y=()=>{const{config:e,updateConfig:t}=(0,m.UK)();return(0,n.useEffect)((()=>{if("Google"===e?.webMode){const a={};void 0===e?.serpMaxLinksToVisit&&(a.serpMaxLinksToVisit=3),void 0===e?.webLimit&&(a.webLimit=16),Object.keys(a).length>0&&t(a)}}),[e?.webMode,e?.serpMaxLinksToVisit,e?.webLimit,t]),(0,s.jsxs)(c,{value:"web-search",className:(0,i.cn)("bg-[var(--input-background)] border-[var(--text)]/10 rounded-xl shadow-md","overflow-hidden","transition-all duration-150 ease-in-out","hover:border-[var(--active)] hover:brightness-105"),children:[(0,s.jsx)(d,{className:(0,i.cn)("flex items-center justify-between w-full px-3 py-2 hover:no-underline","text-[var(--text)] font-medium","hover:brightness-95"),children:(0,s.jsx)(w,{icon:"🌐",text:"Web Search"})}),(0,s.jsx)(u,{className:"px-3 pb-4 pt-2 text-[var(--text)]",children:(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)(V,{updateConfig:t,webMode:e?.webMode}),"Google"===e?.webMode?(0,s.jsx)("div",{className:"w-[45%] pl-4 flex flex-col space-y-6",children:(0,s.jsx)(H,{config:e,updateConfig:t})}):(0,s.jsx)("div",{className:"w-[45%] pl-4",children:(0,s.jsx)("p",{className:"text-[var(--text)]/70",children:"Select a search mode to see its options."})})]})})]})},K=()=>{const{config:e}=(0,m.UK)(),[t,a]=(0,n.useState)(!e?.models||0===e.models.length),[r,o]=(0,n.useState)("");return(0,s.jsxs)("div",{id:"settings",className:"relative z-[1] top-0 w-full h-full flex-1 flex-col overflow-y-auto overflow-x-hidden bg-background text-foreground px-6 pb-10 pt-6 scrollbar-hidden",children:[t&&(0,s.jsx)("div",{className:(0,i.cn)("mb-6 p-6","rounded-xl","bg-card border border-border shadow-sm","text-foreground"),children:(0,s.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[(0,s.jsx)("h2",{className:"text-apple-title3 font-semibold text-center",children:"Quick Setup Guide"}),(0,s.jsxs)("div",{className:"flex flex-col gap-4 w-full",children:[(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)("span",{className:"flex-shrink-0 w-6 h-6 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-apple-footnote font-medium",children:"1"}),(0,s.jsx)("p",{className:"text-apple-callout text-muted-foreground",children:"Fill your API key or URLs in API Access"})]}),(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)("span",{className:"flex-shrink-0 w-6 h-6 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-apple-footnote font-medium",children:"2"}),(0,s.jsx)("p",{className:"text-apple-callout text-muted-foreground",children:"Exit settings, then use the persona selector to choose your model and start chatting"})]}),(0,s.jsx)("div",{className:"text-apple-footnote text-muted-foreground mt-2 ml-9 italic",children:"Note: You can change other settings now or later. Have fun!"})]}),(0,s.jsx)(g.$,{variant:"default",className:"bg-primary text-primary-foreground hover:bg-primary/90 rounded-lg px-6 py-2 text-apple-callout font-medium",onClick:()=>{o("connect"),a(!1)},children:"Get Started"})]})}),(0,s.jsxs)(l,{type:"single",collapsible:!0,className:"w-full flex flex-col gap-4",value:r,onValueChange:o,children:[(0,s.jsx)(N,{}),(0,s.jsx)(T,{}),(0,s.jsx)(U,{}),(0,s.jsx)(A,{}),(0,s.jsx)(Y,{}),(0,s.jsx)("div",{className:"pointer-events-none h-12"})," "]})]})}},3885:(e,t,a)=>{a.d(t,{Bc:()=>o,ZI:()=>c,k$:()=>l,m_:()=>i});var s=a(4848),n=(a(6540),a(3881)),r=a(5284);function o({delayDuration:e=500,...t}){return(0,s.jsx)(n.Kq,{"data-slot":"tooltip-provider",delayDuration:e,...t})}function i({...e}){return(0,s.jsx)(o,{children:(0,s.jsx)(n.bL,{"data-slot":"tooltip",...e})})}function l({...e}){return(0,s.jsx)(n.l9,{"data-slot":"tooltip-trigger",...e})}function c({className:e,sideOffset:t=0,children:a,...o}){return(0,s.jsx)(n.ZL,{children:(0,s.jsxs)(n.UC,{"data-slot":"tooltip-content",sideOffset:t,className:(0,r.cn)("bg-primary/50 text-primary-foreground border-transparent animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-2 py-1 text-xs text-balance",e),...o,children:[a,(0,s.jsx)(n.i3,{className:"bg-primary fill-primary z-50 size-1 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}},4339:(e,t,a)=>{a.d(t,{p:()=>S});var s=a(4848),n=a(6540),r=a(6973),o=a(6948),i=a(2090),l=a(3885),c=a(9696),d=a(5284),u=a(6555),m=a(9014);function h({className:e,...t}){return(0,s.jsx)(m.bL,{"data-slot":"switch",className:(0,d.cn)("peer relative inline-flex h-[10px] w-[26px] shrink-0 cursor-pointer items-center rounded-full bg-input transition-colors duration-200 data-[state=checked]:bg-primary data-[state=unchecked]:bg-foreground/10 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,s.jsx)(m.zi,{"data-slot":"switch-thumb",className:(0,d.cn)("pointer-events-none block size-4 rounded-full shadow-md ring-1 transition-transform duration-200 ease-in-out transform","data-[state=checked]:translate-x-[12px] data-[state=checked]:bg-white data-[state=checked]:ring-primary/50","data-[state=unchecked]:translate-x-[0px] data-[state=unchecked]:bg-primary data-[state=unchecked]:ring-primary-foreground/50")})})}var p=a(6532),g=a(5634),f=a(37),x=a(888),v=a(3732),b=a(2955);const y=()=>{const{config:e,updateConfig:t}=(0,o.UK)(),[a,r]=(0,n.useState)(!1),[m,y]=(0,n.useState)(e.noteContent||""),[w,j]=(0,n.useState)(""),[N,C]=(0,n.useState)("");(0,n.useEffect)((()=>{a?(y(e.noteContent||""),C(e.popoverTitleDraft||""),j(e.popoverTagsDraft||"")):(e.noteContent!==m&&C(""),j(""))}),[a,e]);const S=m===(e.noteContent||""),k=N===(e.popoverTitleDraft||""),$=w===(e.popoverTagsDraft||""),M=S&&k&&$,A=!N.trim()&&!m.trim()&&!w.trim(),E=!!N.trim()||!!m.trim()||!!w.trim(),T=!(!e.popoverTitleDraft||!e.popoverTitleDraft.trim())||!(!e.noteContent||!e.noteContent.trim())||!(!e.popoverTagsDraft||!e.popoverTagsDraft.trim()),z=!E&&!T;return(0,s.jsx)(l.Bc,{delayDuration:500,children:(0,s.jsxs)(u.AM,{open:a,onOpenChange:r,children:[(0,s.jsxs)(l.m_,{children:[(0,s.jsx)(l.k$,{asChild:!0,children:(0,s.jsx)(u.Wv,{asChild:!0,children:(0,s.jsx)(i.$,{variant:"ghost",size:"sm",className:(0,d.cn)("rounded-md not-focus-visible",e.useNote?"text-[var(--active)] hover:bg-muted/80":"text-foreground hover:text-foreground hover:bg-[var(--text)]/10"),"aria-label":"Toggle/Edit Note",children:(0,s.jsx)(f.yVo,{})})})}),(0,s.jsx)(l.ZI,{side:"top",className:"bg-secondary/50 text-foreground",children:(0,s.jsx)("p",{children:"Toggle/Edit Note"})})]}),(0,s.jsx)(u.hl,{className:"w-[80vw] p-4 bg-[var(--bg)] border-[var(--text)]/10 shadow-lg rounded-md",side:"top",align:"end",sideOffset:5,children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)(g.J,{htmlFor:"use-note-switch",className:"text-[var(--text)] font-medium cursor-pointer",children:"Use Note in Chat"}),(0,s.jsx)(h,{id:"use-note-switch",checked:e.useNote||!1,onCheckedChange:e=>{t({useNote:e})}})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(p.p,{id:"popover-title-input",type:"text",placeholder:"Title (optional)",value:N,onChange:e=>C(e.target.value),className:"mb-2 bg-[var(--input-bg)] border-[var(--text)]/10 text-[var(--text)] focus-visible:ring-1 focus-visible:ring-[var(--active)]"}),(0,s.jsx)(c.T,{id:"note-popover-textarea",value:m,onChange:e=>y(e.target.value),placeholder:"Persistent notes for the AI...",className:"mt-1 min-h-[30vh] max-h-[70vh] overflow-y-auto bg-[var(--input-bg)] border-[var(--text)]/10 text-[var(--text)] focus-visible:ring-1 focus-visible:ring-[var(--active)] resize-none thin-scrollbar"})]}),(0,s.jsx)("div",{children:(0,s.jsx)(p.p,{id:"popover-tags-input",type:"text",placeholder:"Tags (comma-separated)",value:w,onChange:e=>j(e.target.value),className:"mt-2 bg-[var(--input-bg)] border-[var(--text)]/10 text-[var(--text)] focus-visible:ring-1 focus-visible:ring-[var(--active)]"})}),(0,s.jsx)("div",{className:"flex justify-between items-center pt-1",children:(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)(i.$,{variant:"outline",onClick:()=>{y(""),C(""),j(""),t({noteContent:"",popoverTitleDraft:"",popoverTagsDraft:""}),(0,x.oR)("Note cleared")},disabled:z,className:(0,d.cn)("border-[var(--border)] text-[var(--text)]","text-xs px-2 py-1 h-auto w-16"),children:"Clear"}),(0,s.jsx)(i.$,{variant:"outline",onClick:()=>{t({noteContent:m,popoverTitleDraft:N,popoverTagsDraft:w}),x.oR.success("Draft saved!")},className:(0,d.cn)("border-[var(--border)] text-[var(--text)]","text-xs px-2 py-1 h-auto w-16"),disabled:M,children:"Save"}),(0,s.jsxs)(l.m_,{children:[(0,s.jsx)(l.k$,{asChild:!0,children:(0,s.jsx)(i.$,{variant:"ghost",onClick:async()=>{if(chrome.runtime.sendMessage({type:"SAVE_NOTE_TO_FILE",payload:{content:m}}),x.oR.success("Note saved to file!"),m.trim())try{const e=(new Date).toLocaleString([],{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),a=N.trim()||`Note from Popover - ${e}`,s=""===w.trim()?[]:w.split(",").map((e=>e.trim())).filter((e=>e.length>0));await(0,b.s2)({title:a,content:m,tags:s}),x.oR.success("Snapshot saved to Note System!"),y(""),C(""),j(""),t({noteContent:"",popoverTitleDraft:"",popoverTagsDraft:""})}catch(e){console.error("Error saving note to system from popover:",e),x.oR.error("Failed to save note to system.")}r(!1)},disabled:A,className:(0,d.cn)("text-xs px-2 py-1 h-auto w-10"),children:(0,s.jsx)(v.Zuq,{size:16})})}),(0,s.jsx)(l.ZI,{side:"top",className:"bg-secondary/50 text-foreground",children:"Save to File"})]})]})})]})})]})})};var w=a(9018),j=a(69),N=a(7520);const C=()=>{const{config:e,updateConfig:t}=(0,o.UK)(),a=e?.persona||"default",n=e?.personas||{},r=N.z[a]||N.z.default;return(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsxs)(j.eu,{className:"h-8 w-8 border border-border",children:[(0,s.jsx)(j.BK,{src:r,alt:a}),(0,s.jsx)(j.q5,{className:"text-apple-footnote font-medium",children:("default"===a?"C":a.substring(0,1)).toUpperCase()})]}),(0,s.jsxs)(w.l6,{value:a,onValueChange:e=>{t({persona:e})},children:[(0,s.jsx)(w.bq,{className:(0,d.cn)("w-auto min-w-[120px] border-none bg-transparent shadow-none","text-apple-callout font-medium text-foreground","hover:bg-secondary/50 rounded-lg px-3 py-1 h-8","focus:ring-2 focus:ring-primary/20 focus:border-primary/50"),children:(0,s.jsx)(w.yv,{placeholder:"Select persona"})}),(0,s.jsx)(w.gC,{className:"bg-popover border border-border shadow-lg rounded-lg",children:Object.keys(n).map((e=>(0,s.jsx)(w.eb,{value:e,className:(0,d.cn)("text-apple-callout text-popover-foreground","hover:bg-accent hover:text-accent-foreground","focus:bg-accent focus:text-accent-foreground","cursor-pointer rounded-md"),children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)(j.eu,{className:"h-5 w-5",children:[(0,s.jsx)(j.BK,{src:N.z[e]||N.z.default,alt:e}),(0,s.jsx)(j.q5,{className:"text-xs",children:("default"===e?"C":e.substring(0,1)).toUpperCase()})]}),"default"===e?"Chromepanion":e]})},e)))})]})]})},S=({isLoading:e,message:t,setMessage:a,onSend:u,onStopRequest:m})=>{const{config:h}=(0,o.UK)(),p=(0,n.useRef)(null),[g,f]=(0,n.useState)(!1);return(0,n.useEffect)((()=>{p.current?.focus()}),[t,h?.chatMode]),(0,s.jsxs)("div",{className:"flex flex-col gap-3 mb-4",children:[(0,s.jsx)(C,{}),(0,s.jsxs)("div",{className:(0,d.cn)("flex w-full border border-border items-center gap-2 p-3 bg-card rounded-xl shadow-sm",g&&"ring-2 ring-primary/20 border-primary/50"),children:[(0,s.jsx)(c.T,{autosize:!0,ref:p,minRows:1,maxRows:8,autoComplete:"off",id:"user-input",placeholder:"Search the web with AI...",value:t,autoFocus:!0,onChange:e=>a(e.target.value),onKeyDown:a=>{e||"Enter"!==a.key||!t.trim()||a.altKey||a.metaKey||a.shiftKey||(a.preventDefault(),a.stopPropagation(),u())},className:"flex-grow bg-transparent border-none shadow-none outline-none focus-visible:ring-0 resize-none text-apple-body",onFocus:()=>f(!0),onBlur:()=>f(!1)}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(y,{}),(0,s.jsx)(l.Bc,{delayDuration:300,children:(0,s.jsxs)(l.m_,{children:[(0,s.jsx)(l.k$,{asChild:!0,children:(0,s.jsx)(i.$,{"aria-label":"Send",variant:"ghost",size:"sm",className:(0,d.cn)("p-2 rounded-lg h-8 w-8 flex items-center justify-center",e?"text-destructive hover:bg-destructive/10":"text-primary hover:bg-primary/10",!e&&!t.trim()&&"opacity-50"),onClick:a=>{a.stopPropagation(),e?m():t.trim()&&u()},disabled:!e&&!t.trim(),children:e?(0,s.jsx)(r.wO6,{className:"h-4 w-4"}):(0,s.jsx)(r.B07,{className:"h-4 w-4"})})}),(0,s.jsx)(l.ZI,{side:"top",className:"bg-popover text-popover-foreground border border-border",children:(0,s.jsx)("p",{className:"text-apple-footnote",children:e?"Stop":"Send"})})]})})]})]})]})}},4539:(e,t,a)=>{a.d(t,{F:()=>o});var s=a(4848),n=(a(6540),a(6627)),r=a(5284);function o({className:e,children:t,viewportRef:a,...o}){return(0,s.jsxs)(n.bL,{"data-slot":"scroll-area",className:(0,r.cn)("relative",e),...o,children:[(0,s.jsx)(n.LM,{ref:a,"data-slot":"scroll-area-viewport",className:(0,r.cn)("size-full rounded-[inherit]","focus:outline-none focus-visible:outline-none focus-visible:ring-0 focus-visible:shadow-none","[&>div]:!border-b-0","pb-px pr-px"),children:t}),(0,s.jsx)(i,{orientation:"vertical"}),(0,s.jsx)(i,{orientation:"horizontal"}),(0,s.jsx)(n.OK,{})]})}function i({className:e,orientation:t="vertical",...a}){return(0,s.jsx)(n.VM,{"data-slot":"scroll-area-scrollbar",orientation:t,className:(0,r.cn)("flex touch-none select-none transition-colors","vertical"===t&&"h-full w-px","horizontal"===t&&"h-px w-full border-b-0 bg-transparent shadow-none min-h-0",e),...a,children:(0,s.jsx)(n.lr,{"data-slot":"scroll-area-thumb",className:"relative flex-1 rounded-sm"})})}},5095:(e,t,a)=>{a.d(t,{e:()=>o});var s=a(6540),n=a(888),r=a(6948);const o=()=>{const{config:e,updateConfig:t}=(0,r.UK)();return{appendToNote:(0,s.useCallback)((a=>{if(!a||""===a.trim())return void n.oR.error("No text selected to add to note.");const s=e.noteContent||"",r=s+(s&&a.trim()?"\n\n":"")+a.trim();t({noteContent:r}),n.oR.success("Selected text appended to note.")}),[e.noteContent,t])}}},5284:(e,t,a)=>{a.d(t,{cn:()=>r});var s=a(4164),n=a(856);function r(...e){return(0,n.QP)((0,s.$)(e))}},5431:(e,t,a)=>{a.d(t,{A:()=>s});const s={getItem:async e=>{const t=(await chrome.storage.local.get(e))[e];if(null==t)return null;try{return"string"==typeof t?t:JSON.stringify(t)}catch(e){return null}},setItem:async(e,t)=>{const a="string"==typeof t?t:JSON.stringify(t);await chrome.storage.local.set({[e]:a})},deleteItem:async e=>{await chrome.storage.local.remove(e)}}},5634:(e,t,a)=>{a.d(t,{J:()=>o});var s=a(4848),n=(a(6540),a(5920)),r=a(5284);function o({className:e,...t}){return(0,s.jsx)(n.b,{"data-slot":"label",className:(0,r.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},5886:(e,t,a)=>{a.d(t,{z2:()=>l});var s=a(38);const n={isLoaded:!1},r=(0,s.Z0)({name:"content",initialState:n,reducers:{reset:()=>n,contentLoaded:e=>{e.isLoaded=!0}}}),{actions:o,reducer:i}=r,l={}},6108:(e,t,a)=>{a.d(t,{z2:()=>d});var s,n,r=a(38);!function(e){e.Default="default",e.ConfirmDeleteCard="confirmDeleteCard"}(s||(s={})),function(e){e.Default="default"}(n||(n={}));const o={isOpen:!1},i=(0,r.Z0)({name:"sidePanel",initialState:o,reducers:{reset:()=>o}}),{actions:l,reducer:c}=i,d={}},6174:(e,t,a)=>{var s;a.d(t,{A:()=>n}),function(e){e.ContentPort="content",e.SidePanelPort="sidePanel",e.SAVE_NOTE_TO_FILE="save-note-to-file"}(s||(s={}));const n=s},6508:(e,t,a)=>{a.d(t,{AC:()=>l,Af:()=>c});var s=a(4848),n=a(6540),r=a(3),o=a(2090),i=a(5284);const l=e=>{const{children:t,className:a,wrapperClassName:l,buttonVariant:c="ghost",buttonClassName:d,...u}=e,[m,h]=(0,n.useState)(!1),[p,g]=(0,n.useState)(!1),f=n.Children.only(t);let x="";return f?.props?.children&&(x=Array.isArray(f.props.children)?f.props.children.map((e=>"string"==typeof e?e:"")).join(""):String(f.props.children),x=x.trim()),(0,s.jsxs)("div",{className:(0,i.cn)("relative my-4",l),onMouseEnter:()=>g(!0),onMouseLeave:()=>g(!1),children:[(0,s.jsx)("pre",{className:(0,i.cn)("p-3 rounded-md overflow-x-auto thin-scrollbar",a),...u,children:t}),x&&(0,s.jsx)(o.$,{variant:c,size:"sm","aria-label":m?"Copied!":"Copy code",title:m?"Copied!":"Copy code",className:(0,i.cn)("absolute right-2 top-2 h-8 w-8 p-0","transition-opacity duration-200",p||m?"opacity-100 pointer-events-auto":"opacity-0 pointer-events-none",d),onClick:()=>{x&&(navigator.clipboard.writeText(x),h(!0),setTimeout((()=>h(!1)),1500))},children:m?(0,s.jsx)(r.YrT,{className:"h-4 w-4"}):(0,s.jsx)(r.nxz,{className:"h-4 w-4"})})]})},c={ul:({children:e,className:t,...a})=>(0,s.jsx)("ul",{className:(0,i.cn)("list-disc pl-5 my-2",t),...a,children:e}),ol:({children:e,className:t,...a})=>(0,s.jsx)("ol",{className:(0,i.cn)("list-decimal pl-5 my-2",t),...a,children:e}),p:({children:e,className:t,...a})=>(0,s.jsx)("p",{className:(0,i.cn)("mb-0",t),...a,children:e}),pre:l,code:e=>{const{children:t,className:a,inline:n,...r}=e;return n?(0,s.jsx)("code",{className:(0,i.cn)("px-1 py-0.5 rounded-sm bg-[var(--code-inline-bg)] text-[var(--code-inline-text)] text-sm",a),...r,children:t}):(0,s.jsx)("code",{className:(0,i.cn)("font-mono text-sm",a),...r,children:t})},a:({children:e,href:t,className:a,...n})=>(0,s.jsx)("a",{href:t,className:(0,i.cn)("text-[var(--link)] hover:underline",a),target:"_blank",rel:"noopener noreferrer",...n,children:e}),strong:({children:e,className:t,...a})=>(0,s.jsx)("strong",{className:(0,i.cn)("font-bold",t),...a,children:e}),em:({children:e,className:t,...a})=>(0,s.jsx)("em",{className:(0,i.cn)("italic",t),...a,children:e}),h1:({children:e,className:t,...a})=>(0,s.jsx)("h1",{className:(0,i.cn)("text-2xl font-bold mt-4 mb-2 border-b pb-1 border-[var(--border)]",t),...a,children:e}),h2:({children:e,className:t,...a})=>(0,s.jsx)("h2",{className:(0,i.cn)("text-xl font-semibold mt-3 mb-1 border-b pb-1 border-[var(--border)]",t),...a,children:e}),h3:({children:e,className:t,...a})=>(0,s.jsx)("h3",{className:(0,i.cn)("text-lg font-semibold mt-2 mb-1 border-b pb-1 border-[var(--border)]",t),...a,children:e}),table:({children:e,className:t,...a})=>(0,s.jsx)("div",{className:"markdown-table-wrapper my-2 overflow-x-auto",children:(0,s.jsx)("table",{className:(0,i.cn)("w-full border-collapse border border-[var(--border)]",t),...a,children:e})}),thead:({children:e,className:t,...a})=>(0,s.jsx)("thead",{className:(0,i.cn)("bg-[var(--muted)]",t),...a,children:e}),tbody:({children:e,className:t,...a})=>(0,s.jsx)("tbody",{className:(0,i.cn)(t),...a,children:e}),tr:e=>(0,s.jsx)("tr",{className:(0,i.cn)("border-b border-[var(--border)] even:bg-[var(--muted)]/50",e.className),...e}),th:({children:e,className:t,...a})=>(0,s.jsx)("th",{className:(0,i.cn)("p-2 border border-[var(--border)] text-left font-semibold",t),...a,children:e}),td:({children:e,className:t,...a})=>(0,s.jsx)("td",{className:(0,i.cn)("p-2 border border-[var(--border)]",t),...a,children:e}),blockquote:({children:e,className:t,...a})=>(0,s.jsx)("blockquote",{className:(0,i.cn)("pl-4 italic border-l-4 border-[var(--border)] my-2 text-[var(--muted-foreground)]",t),...a,children:e})}},6532:(e,t,a)=>{a.d(t,{p:()=>o});var s=a(4848),n=a(6540),r=a(5284);function o({className:e,type:t,...a}){const[o,i]=n.useState(!1);return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground flex h-8 w-full min-w-0 rounded-md bg-transparent px-3 py-1 text-sm transition-[color,box-shadow,border-color] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","border border-[var(--text)]/10 dark:border-0","focus-visible:border-ring","text-[var(--text)] px-2.5","focus:border-[var(--active)] dark:focus:border-0 focus:ring-1 focus:ring-[var(--active)] focus:ring-offset-0","hover:border-[var(--active)] dark:hover:border-0","bg-[var(--input-background)]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive","shadow-[var(--input-base-shadow)]",e,o&&"input-breathing"),onFocus:e=>{i(!0),a.onFocus?.(e)},onBlur:e=>{i(!1),a.onBlur?.(e)},...a})}},6555:(e,t,a)=>{a.d(t,{AM:()=>o,Wv:()=>i,hl:()=>l});var s=a(4848),n=(a(6540),a(9823)),r=a(5284);function o({...e}){return(0,s.jsx)(n.bL,{"data-slot":"popover",...e})}function i({...e}){return(0,s.jsx)(n.l9,{"data-slot":"popover-trigger",...e})}function l({className:e,align:t="center",sideOffset:a=4,...o}){return(0,s.jsx)(n.ZL,{children:(0,s.jsx)(n.UC,{"data-slot":"popover-content",align:t,sideOffset:a,className:(0,r.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden",e),...o})})}},6948:(e,t,a)=>{a.d(t,{UK:()=>d,sG:()=>c});var s=a(4848),n=a(6540),r=a(5431);const o=(0,n.createContext)({}),i={Scholar:"You are The Scholar, an analytical academic researcher specializing in web search analysis. When analyzing search results, provide thorough academic-style analysis with structured insights. Behavior: Organize findings into clear sections with proper citations. Analyze credibility of sources and highlight methodological strengths/weaknesses. Present comprehensive summaries with evidence-based conclusions. Include relevant statistics and data points. Mannerisms: Use formal academic tone. Structure responses with clear headings. Always cite sources and assess their reliability.",Executive:"You are The Executive, a strategic business leader focused on actionable intelligence. When analyzing search results, distill information into concise strategic insights. Behavior: Identify key business implications and market opportunities. Provide executive summaries with clear recommendations. Focus on competitive advantages and strategic positioning. Highlight actionable next steps. Mannerisms: Be direct and results-oriented. Use bullet points for clarity. Think in terms of ROI and strategic value.",Storyteller:"You are The Storyteller, a master of engaging narrative who makes information accessible. When analyzing search results, weave findings into compelling stories. Behavior: Create narrative flow that connects different pieces of information. Use analogies and examples to illustrate complex concepts. Make dry data engaging through storytelling techniques. Connect information to human experiences. Mannerisms: Use vivid language and metaphors. Create logical narrative progression. Make complex topics relatable.",Skeptic:'You are The Skeptic, a critical analyst who questions everything. When analyzing search results, highlight biases, contradictions, and missing information. Behavior: Identify potential conflicts of interest in sources. Point out logical fallacies and weak evidence. Highlight what information is missing or unclear. Question assumptions and challenge conventional wisdom. Mannerisms: Use phrases like "However," "It should be noted," and "The evidence suggests." Always present counterarguments.',Mentor:"You are The Mentor, an educational guide focused on learning and growth. When analyzing search results, explain concepts clearly with supportive guidance. Behavior: Break down complex topics into digestible lessons. Provide context and background information. Offer learning resources and next steps. Encourage deeper exploration of topics. Mannerisms: Use encouraging language. Provide step-by-step explanations. Include educational tips and learning opportunities.",Investigator:"You are The Investigator, a methodical fact-checker focused on source credibility. When analyzing search results, systematically verify information and assess reliability. Behavior: Cross-reference information across multiple sources. Evaluate source credibility and potential biases. Identify primary vs. secondary sources. Flag unverified claims and missing evidence. Mannerisms: Use systematic approach to verification. Clearly distinguish between verified facts and claims. Provide source reliability assessments.",Pragmatist:'You are The Pragmatist, a solution-focused analyst emphasizing practical applications. When analyzing search results, focus on actionable insights and real-world implementation. Behavior: Identify practical solutions and implementation strategies. Focus on cost-effective and feasible approaches. Provide step-by-step action plans. Consider resource requirements and constraints. Mannerisms: Use practical language. Focus on "how-to" guidance. Emphasize feasibility and implementation.',Spike:"You are Spike, a capable and versatile executor. Your role is to turn user prompts into actionable results. Behavior: First, correct or clarify the user’s prompt for better accuracy. Add helpful criteria to guide execution. Then, act on the improved prompt as effectively as possible. Mannerisms: Be concise, critical, and sharp. Skip fluff. Use simple, direct language. Focus on feasibility and correctness. When in doubt, fix it and move forward.",Enthusiast:'You are The Enthusiast, an energetic discoverer who presents findings with excitement. When analyzing search results, highlight fascinating discoveries and breakthrough insights. Behavior: Emphasize exciting developments and innovations. Connect findings to broader trends and possibilities. Celebrate interesting discoveries and connections. Inspire curiosity about the topic. Mannerisms: Use enthusiastic language and exclamation points. Highlight "amazing" and "fascinating" aspects. Express genuine excitement about discoveries.',Curator:"You are The Curator, a sophisticated synthesizer of premium insights. When analyzing search results, provide refined, high-quality analysis with elegant presentation. Behavior: Select only the most valuable and relevant information. Present insights with sophisticated analysis and nuanced understanding. Focus on quality over quantity. Provide polished, professional summaries. Mannerisms: Use refined language and elegant phrasing. Focus on premium insights. Present information with sophisticated analysis.",Friend:'You are The Friend, a casual conversationalist sharing interesting discoveries. When analyzing search results, present findings in a friendly, approachable manner. Behavior: Share information like you would with a close friend. Use conversational tone and relatable examples. Make complex topics feel accessible and interesting. Include personal observations and casual insights. Mannerisms: Use casual, friendly language. Include phrases like "You know what\'s interesting?" and "I found this cool thing." Make information feel like a friendly conversation.'},l={personas:i,generateTitle:!0,backgroundImage:!1,animatedBackground:!1,persona:"Scholar",webMode:"Google",webLimit:60,serpMaxLinksToVisit:3,contextLimit:60,maxTokens:32480,temperature:.7,topP:.95,presencepenalty:0,models:[],selectedModel:void 0,chatMode:"web",ollamaUrl:"http://localhost:11434",ollamaConnected:!1,fontSize:14,panelOpen:!1,computeLevel:"low",useNote:!1,noteContent:"",userName:"user",userProfile:"",popoverTitleDraft:"",popoverTagsDraft:""},c=({children:e})=>{const[t,a]=(0,n.useState)(l),[c,d]=(0,n.useState)(!0);return(0,n.useEffect)((()=>{(async()=>{try{const e=await r.A.getItem("config"),t=(e=>{const t=["Ein","Warren","Sherlock","Agatha","Jet","Faye","Jan"];return e.personas&&Object.keys(e.personas).some((e=>t.includes(e)))&&(e.personas=i),e.persona&&t.includes(e.persona)&&(e.persona="Scholar"),e.personas={...i,...e.personas},e})(e?JSON.parse(e):l);a(t),e&&await r.A.setItem("config",JSON.stringify(t))}catch(e){console.error("Failed to load config",e),a(l)}finally{d(!1)}})()}),[]),(0,n.useEffect)((()=>{const e=t?.fontSize||l.fontSize;document.documentElement.style.setProperty("font-size",`${e}px`)}),[c,t?.fontSize]),c?(0,s.jsx)("div",{children:"Loading..."}):(0,s.jsx)(o,{value:{config:t,updateConfig:e=>{a((t=>{const a={...t,...e};return r.A.setItem("config",JSON.stringify(a)).catch((e=>console.error("Failed to save config",e))),a}))}},children:e})},d=()=>(0,n.use)(o)},7086:(e,t,a)=>{a.d(t,{Cf:()=>h,Es:()=>g,L3:()=>f,c7:()=>p,lG:()=>d,rr:()=>x});var s=a(4848),n=a(6540),r=a(990),o=a(8697),i=a(5284);const l={default:"bg-black/50",darker:"bg-black/60"},c={default:"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",themedPanel:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full translate-x-[-50%] translate-y-[-50%] gap-4 duration-200","bg-[var(--bg)] text-[var(--text)] border-[var(--text)]","rounded-lg shadow-xl p-0")};function d({...e}){return(0,s.jsx)(r.bL,{"data-slot":"dialog",...e})}function u({...e}){return(0,s.jsx)(r.ZL,{"data-slot":"dialog-portal",...e})}const m=n.forwardRef((({className:e,variant:t="default",...a},n)=>(0,s.jsx)(r.hJ,{ref:n,"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50",l[t],e),...a})));m.displayName=r.hJ.displayName;const h=n.forwardRef((({className:e,children:t,variant:a="default",...n},l)=>(0,s.jsxs)(u,{"data-slot":"dialog-portal",children:[(0,s.jsx)(m,{variant:"themedPanel"===a?"darker":"default"}),(0,s.jsxs)(r.UC,{ref:l,"data-slot":"dialog-content",className:(0,i.cn)(c[a],e),...n,children:[t,(0,s.jsxs)(r.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,s.jsx)(o.A,{}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})));function p({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center",e),...t})}function g({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"dialog-footer",className:(0,i.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t})}function f({className:e,...t}){return(0,s.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",e),...t})}function x({className:e,...t}){return(0,s.jsx)(r.VY,{"data-slot":"dialog-description",className:(0,i.cn)("text-muted-foreground text-sm",e),...t})}h.displayName=r.UC.displayName},7520:(e,t,a)=>{a.d(t,{z:()=>s});const s={Scholar:"assets/images/chromepanion.png",Executive:"assets/images/chromepanion.png",Storyteller:"assets/images/chromepanion.png",Skeptic:"assets/images/chromepanion.png",Mentor:"assets/images/chromepanion.png",Investigator:"assets/images/chromepanion.png",Pragmatist:"assets/images/chromepanion.png",Enthusiast:"assets/images/chromepanion.png",Curator:"assets/images/chromepanion.png",Friend:"assets/images/chromepanion.png",Spike:"assets/images/chromepanion.png",default:"assets/images/chromepanion.png"}},7660:(e,t,a)=>{a.a(e,(async(e,s)=>{try{a.d(t,{GV:()=>d,ii:()=>u,mR:()=>l,xD:()=>c});var n=a(2506),r=a(5431);const e=()=>(new Date).toJSON().slice(0,19).replace("T","_").replace(/:/g,"-");let o="assistant",i="user";try{const e=await r.A.getItem("config");if(e){const t=JSON.parse(e);t.persona&&"string"==typeof t.persona&&""!==t.persona.trim()&&(o=t.persona),t.userName&&"string"==typeof t.userName&&""!==t.userName.trim()&&(i=t.userName)}}catch(e){console.error("Failed to load config to get persona name for download:",e)}const l=async t=>{if(!t||0===t.length)return;const a=t.map((e=>{let t=`${"assistant"===e.role?o:"user"===e.role?i:e.role}:\n`;return"assistant"===e.role&&e.webDisplayContent&&(t+=`~From the Internet~\n${e.webDisplayContent}\n\n---\n\n`),t+=e.rawContent,t})).join("\n\n"),s=document.createElement("a");s.setAttribute("href",`data:text/plain;charset=utf-8,${encodeURIComponent(a)}`);const n=`chat_${e()}.txt`;s.setAttribute("download",n),s.style.display="none",document.body.appendChild(s),s.click(),document.body.removeChild(s)},c=t=>{if(!t||0===t.length)return;const a=t.map((e=>({...e,role:"assistant"===e.role?o:"user"===e.role?i:e.role}))),s={assistantNameInExport:o,userNameInExport:i,chatHistory:a},n=JSON.stringify(s,null,2),r=document.createElement("a");r.setAttribute("href",`data:application/json;charset=utf-8,${encodeURIComponent(n)}`);const l=`chat_${e()}.json`;r.setAttribute("download",l),r.style.display="none",document.body.appendChild(r),r.click(),document.body.removeChild(r)},d=t=>{if(!t||0===t.length)return;const a=document.querySelectorAll(".chatMessage");if(!a||0===a.length)return void console.warn("No chat messages found to generate image.");const s=document.createElement("div");if(s.style.display="flex",s.style.flexDirection="column",s.style.paddingBottom="1rem",s.style.background=document.documentElement.style.getPropertyValue("--bg"),a[0]){const e=1.2;s.style.width=a[0].offsetWidth*e+"px"}a.forEach((e=>{const t=e.cloneNode(!0);t instanceof HTMLElement?(t.style.marginTop="1rem",t.style.boxSizing="border-box",s.appendChild(t)):console.warn("Cloned node is not an HTMLElement:",t)})),document.body.appendChild(s),(0,n.$E)(s,{filter:function(e){if(e instanceof Element){const t=e.getAttribute("aria-label");if(t&&["Copy code","Copied!","Save edit","Cancel edit"].includes(t))return!1}return!0},pixelRatio:2,style:{margin:"0",padding:s.style.paddingBottom},backgroundColor:document.documentElement.style.getPropertyValue("--bg")||"#ffffff"}).then((t=>{const a=document.createElement("a");a.setAttribute("href",t);const s=`chat_${e()}.png`;a.setAttribute("download",s),a.style.display="none",document.body.appendChild(a),a.click(),document.body.removeChild(a)})).catch((e=>{console.error("Oops, something went wrong generating the image!",e)})).finally((()=>{document.body.contains(s)&&document.body.removeChild(s)}))},u=t=>{if(!t||0===t.length)return;const a=t.map((e=>{const t=`### ${"assistant"===e.role?o:"user"===e.role?i:e.role}`;let a=e.rawContent;return a=a.replace(/```([\s\S]*?)```/g,"\n```$1```\n"),a=a.replace(/(https?:\/\/[^\s]+)/g,"[Link]($1)"),`${t}\n\n${a}\n`})).join("\n---\n\n"),s=document.createElement("a");s.setAttribute("href",`data:text/markdown;charset=utf-8,${encodeURIComponent(a)}`),s.setAttribute("download",`chat_${e()}.md`),s.style.display="none",document.body.appendChild(s),s.click(),document.body.removeChild(s)};s()}catch(e){s(e)}}),1)},8473:(e,t,a)=>{a.d(t,{D:()=>p});var s=a(4848),n=a(6540),r=a(7211),o=a(2090),i=a(4539),l=a(6250),c=a(3790),d=a.n(c),u=a(6532);const m=e=>new Date(e).toLocaleDateString("sv-SE"),h=12,p=({loadChat:e,onDeleteAll:t,className:a})=>{const[c,p]=(0,n.useState)([]),[g,f]=(0,n.useState)(""),[x,v]=(0,n.useState)(1),[b,y]=(0,n.useState)(null),[w,j]=(0,n.useState)(null),N=(0,n.useCallback)((e=>{const t=e.sort(((e,t)=>t.last_updated-e.last_updated));p(t)}),[]);(0,n.useEffect)((()=>{(async()=>{try{const e=(await d().keys()).filter((e=>e.startsWith("chat_")));if(0===e.length)return p([]),void v(1);const t=e.map((e=>d().getItem(e))),a=(await Promise.all(t)).filter((e=>null!==e&&"object"==typeof e&&"id"in e&&"last_updated"in e));N(a),v(1)}catch(e){console.error("Error fetching messages:",e),p([])}})()}),[N]);const C=(0,n.useMemo)((()=>{if(!g)return c;const e=g.toLowerCase();return c.filter((t=>{const a=t.title?.toLowerCase().includes(e),s=t.turns.some((t=>t.rawContent.toLowerCase().includes(e)));return a||s}))}),[c,g]);(0,n.useEffect)((()=>{v(1)}),[g]);const S=(0,n.useMemo)((()=>Math.max(1,Math.ceil(C.length/h))),[C]);(0,n.useEffect)((()=>{x>S&&v(S)}),[x,S]);const k=(0,n.useMemo)((()=>{const e=(x-1)*h,t=e+h;return C.slice(e,t)}),[C,x]),$=(0,n.useMemo)((()=>k.map((e=>({...e,date:m(e.last_updated)})))),[k]),M=(0,n.useMemo)((()=>Array.from(new Set($.map((e=>e.date))))),[$]),A=(0,n.useCallback)((async e=>{try{await d().removeItem(e);const t=(await d().keys()).filter((e=>e.startsWith("chat_"))),a=(await Promise.all(t.map((e=>d().getItem(e))))).filter((e=>e&&"object"==typeof e&&"id"in e&&"last_updated"in e&&"turns"in e));N(a);const s=a.filter((e=>{if(!g)return!0;const t=g.toLowerCase(),a=e.title?.toLowerCase().includes(t),s=e.turns.some((e=>e.rawContent.toLowerCase().includes(t)));return a||s})),n=Math.max(1,Math.ceil(s.length/h));let r=x;r>n&&(r=n);const o=(r-1)*h;0===s.slice(o,o+h).length&&r>1&&(r-=1),v(r)}catch(e){console.error("Error deleting message:",e)}}),[N,x,g]),E=(0,n.useCallback)((async()=>{try{const e=(await d().keys()).filter((e=>e.startsWith("chat_")));await Promise.all(e.map((e=>d().removeItem(e)))),p([]),t&&t()}catch(e){console.error("Error deleting all messages:",e)}}),[t]);(0,n.useEffect)((()=>(window.deleteAllChats=E,()=>{window.deleteAllChats===E&&delete window.deleteAllChats})),[E]);const T=(0,n.useCallback)((()=>v((e=>Math.min(e+1,S)))),[S]),z=(0,n.useCallback)((()=>v((e=>Math.max(e-1,1)))),[]),P=`flex flex-col w-full ${a||""}`.trim(),L=e=>{f(e.target.value)};return 0!==c.length||g?0===C.length&&g?(0,s.jsxs)("div",{className:P,children:[(0,s.jsx)("div",{className:"p-0",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(u.p,{type:"text",placeholder:"Search chat history (titles & content)...",value:g,onChange:L,className:"w-full bg-background rounded-none text-foreground placeholder:text-muted-foreground font-['Space_Mono',_monospace] pl-10"}),(0,s.jsx)(l.Huy,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground"})]})}),(0,s.jsx)(i.F,{className:"flex-1 w-full min-h-0",children:(0,s.jsxs)("div",{className:"px-4 pb-4 pt-5 text-center font-['Space_Mono',_monospace] text-foreground/70 h-full flex items-center justify-center",children:['No results found for "',g,'".']})})]}):(0,s.jsxs)("div",{className:P,children:[(0,s.jsx)("div",{className:"p-0",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(u.p,{type:"text",placeholder:"Search chat history (titles & content)...",value:g,onChange:L,className:"w-full bg-background rounded-none text-foreground placeholder:text-muted-foreground font-['Space_Mono',_monospace] pl-10"}),(0,s.jsx)(l.Huy,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground"})]})}),(0,s.jsx)(i.F,{className:"flex-1 w-full min-h-0",children:(0,s.jsx)("div",{className:"px-4 pb-4 font-['Space_Mono',_monospace]",children:M.map((t=>(0,s.jsxs)("div",{className:"mb-3 mt-3",children:[(0,s.jsx)("p",{className:"text-foreground text-lg font-bold overflow-hidden pl-4 pb-1 text-left text-ellipsis whitespace-nowrap w-[90%]",children:t===m(new Date)?"Today":t}),$.filter((e=>e.date===t)).map((t=>(0,s.jsxs)("div",{className:"flex items-center group font-['Space_Mono',_monospace]",onMouseEnter:()=>y(t.id),onMouseLeave:()=>y(null),children:[(0,s.jsxs)("span",{className:"text-foreground text-base font-normal pl-4 w-[4.5rem] flex-shrink-0 font-['Space_Mono',_monospace]",children:[new Date(t.last_updated).getHours().toString().padStart(2,"0"),":",new Date(t.last_updated).getMinutes().toString().padStart(2,"0")]}),(0,s.jsx)("button",{className:`text-foreground text-base font-normal overflow-hidden px-4 py-2 text-left text-ellipsis whitespace-nowrap flex-grow hover:underline hover:underline-offset-4 hover:decoration-1 ${t.id===w?"line-through decoration-2":""} font-['Space_Mono',_monospace]`,onClick:()=>e(t),children:t.title||"Untitled Chat"}),(0,s.jsx)(r.P.div,{className:"shrink-0 transition-opacity duration-150 "+(b===t.id?"opacity-100":"opacity-0 group-hover:opacity-100"),whileHover:{rotate:"15deg"},onMouseEnter:()=>j(t.id),onMouseLeave:()=>j(null),children:(0,s.jsx)(o.$,{variant:"ghost",size:"sm","aria-label":"Delete chat",className:"rounded-full w-8 h-8 font-['Space_Mono',_monospace]",onClick:e=>{e.stopPropagation(),A(t.id)},children:(0,s.jsx)(l.ttk,{className:"h-4 w-4 text-foreground"})})})]},t.id)))]},t)))})}),S>1&&(0,s.jsxs)("div",{className:"flex justify-center items-center h-10 space-x-2 p-2 border-t border-[var(--active)]/50 font-['Space_Mono',_monospace]",children:[(0,s.jsx)(o.$,{onClick:z,disabled:1===x,variant:"ghost",className:"font-['Space_Mono',_monospace]",children:"Prev"}),(0,s.jsxs)("span",{className:"text-md",children:["Page ",x," of ",S]}),(0,s.jsx)(o.$,{onClick:T,disabled:x===S,variant:"ghost",className:"font-['Space_Mono',_monospace]",children:"Next"})]})]}):(0,s.jsxs)("div",{className:P,children:[(0,s.jsx)("div",{className:"p-0",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(u.p,{type:"text",placeholder:"Search chat history (titles & content)...",value:g,onChange:L,className:"w-full bg-background rounded-none text-foreground placeholder:text-muted-foreground font-['Space_Mono',_monospace] pl-10"}),(0,s.jsx)(l.Huy,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground"})]})}),(0,s.jsx)(i.F,{className:"flex-1 w-full min-h-0",children:(0,s.jsx)("div",{className:"px-4 pb-4 pt-5 text-center font-['Space_Mono',_monospace] text-foreground/70 h-full flex items-center justify-center",children:"No chat history found."})})]})}},8639:(e,t,a)=>{a.d(t,{B:()=>w});var s=a(4848),n=a(6540),r=a(3),o=a(1319),i=a(9696),l=a(2090),c=a(5284),d=a(8834);function u({...e}){return(0,s.jsx)(d.bL,{"data-slot":"collapsible",...e})}function m({...e}){return(0,s.jsx)(d.R6,{"data-slot":"collapsible-trigger",...e})}function h({...e}){return(0,s.jsx)(d.Ke,{"data-slot":"collapsible-content",...e})}var p=a(1905),g=a(7736),f=a(6948),x=a(6508);const v=({content:e})=>{const[t,a]=(0,n.useState)(!1);return(0,s.jsx)("div",{className:"mb-2",children:(0,s.jsxs)(u,{open:t,onOpenChange:a,className:"w-full",children:[(0,s.jsx)(m,{asChild:!0,children:(0,s.jsx)(l.$,{variant:"outline",size:"sm",className:(0,c.cn)("mb-1","border-foreground text-foreground hover:text-accent-foreground"),children:t?"Hide Thoughts":"Show Thoughts"})}),(0,s.jsx)(h,{children:(0,s.jsx)("div",{className:(0,c.cn)("p-3 rounded-md border border-dashed","bg-muted","border-muted-foreground","text-muted-foreground"),children:(0,s.jsx)("div",{className:"markdown-body",children:(0,s.jsx)(o.oz,{remarkPlugins:[[p.A,{singleTilde:!1}],g.A],components:b,children:e})})})})]})})},b={...x.Af,pre:e=>(0,s.jsx)(x.AC,{...e,buttonVariant:"copy-button"})},y=({turn:e,index:t,isEditing:a,editText:d,onStartEdit:u,onSetEditText:m,onSaveEdit:h,onCancelEdit:x})=>{const{config:y}=(0,f.UK)(),w=(e.rawContent||"").split(/(<think>[\s\S]*?<\/think>)/g).filter((e=>e&&""!==e.trim())),j=/<think>([\s\S]*?)<\/think>/;return(0,n.useEffect)((()=>{if(!a)return;const e=e=>{"Escape"===e.key?(e.preventDefault(),e.stopPropagation(),x()):"Enter"!==e.key||e.shiftKey||e.altKey||e.metaKey||d.trim()&&(e.preventDefault(),e.stopPropagation(),h())};return document.addEventListener("keydown",e,!0),()=>{document.removeEventListener("keydown",e,!0)}}),[a,x,h,d]),(0,s.jsx)("div",{className:(0,c.cn)("border rounded-2xl text-base","w-[calc(100%-2rem)] mx-1 my-2","pb-1 pl-4 pr-4 pt-1","shadow-lg text-left relative","assistant"===e.role?"bg-accent border-[var(--text)]/20":"bg-primary/10 border-[var(--text)]/20","","chatMessage",a?"editing":"",y&&"number"==typeof y.fontSize&&y.fontSize<=15?"font-semibold":""),onDoubleClick:()=>{a||u(t,e.rawContent)},children:a?(0,s.jsxs)("div",{className:"flex flex-col space-y-2 items-stretch w-full p-1",children:[(0,s.jsx)(i.T,{autosize:!0,value:d,onChange:e=>m(e.target.value),placeholder:"Edit your message...",className:(0,c.cn)("w-full rounded-md border bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground","border-input","text-foreground","hover:border-primary focus-visible:border-primary focus-visible:ring-0","min-h-[60px]"),minRows:3,autoFocus:!0}),(0,s.jsxs)("div",{className:"flex font-mono justify-end space-x-2",children:[(0,s.jsxs)(l.$,{size:"sm",variant:"outline",onClick:h,title:"Save changes",children:[(0,s.jsx)(r.YrT,{className:"h-4 w-4 mr-1"})," Save"]}),(0,s.jsxs)(l.$,{variant:"outline",size:"sm",onClick:x,title:"Discard changes",children:[(0,s.jsx)(r.yGN,{className:"h-4 w-4 mr-1"})," Exit"]})]})]}):(0,s.jsxs)("div",{className:"message-markdown markdown-body relative z-[1] text-foreground",children:["assistant"===e.role&&e.webDisplayContent&&(0,s.jsx)("div",{className:"message-prefix",children:(0,s.jsx)(o.oz,{remarkPlugins:[[p.A,{singleTilde:!1}],g.A],components:b,children:`~From the Internet~\n${e.webDisplayContent}\n\n---\n\n`})}),w.map(((e,t)=>{const a=e.match(j);return a&&a[1]?(0,s.jsx)(v,{content:a[1]},`think_${t}`):(0,s.jsx)("div",{className:"message-content",children:(0,s.jsx)(o.oz,{remarkPlugins:[[p.A,{singleTilde:!1}],g.A],components:b,children:e})},`content_${t}`)}))]})})},w=({turns:e=[],isLoading:t=!1,onReload:a=()=>{},settingsMode:o=!1,onEditTurn:i})=>{const[d,u]=(0,n.useState)(-1),[m,h]=(0,n.useState)(null),[p,g]=(0,n.useState)(""),{config:x}=(0,f.UK)(),v=(0,n.useRef)(null),b=(0,n.useRef)(null);(0,n.useLayoutEffect)((()=>{const e=b.current;e&&e.scrollHeight-e.scrollTop-e.clientHeight<200&&(e.scrollTop=e.scrollHeight)}),[e]);const w=e=>{navigator.clipboard.writeText(e)},j=(e,t)=>{h(e),g(t)},N=()=>{h(null),g("")},C=()=>{null!==m&&p.trim()&&i(m,p),N()};return(0,s.jsxs)("div",{ref:b,id:"messages",className:(0,c.cn)("flex flex-col flex-grow w-full overflow-y-auto pb-2 pt-2","no-scrollbar"),style:{background:"var(--bg)",opacity:o?0:1},children:[e.map(((t,n)=>t&&(0,s.jsxs)("div",{className:(0,c.cn)("flex items-start w-full mt-1 mb-1 px-2 relative","user"===t.role?"justify-start":"justify-end"),onMouseEnter:()=>u(n),onMouseLeave:()=>u(-1),children:["assistant"===t.role&&(0,s.jsxs)("div",{className:(0,c.cn)("flex flex-col items-center self-end space-y-0 mr-0 pb-3 transition-opacity duration-100",d===n?"opacity-100 pointer-events-auto":"opacity-0 pointer-events-none"),children:[m!==n&&(0,s.jsx)(l.$,{"aria-label":"Copy",variant:"message-action",size:"xs",onClick:()=>w(t.rawContent),title:"Copy message",children:(0,s.jsx)(r.nxz,{className:"text-[var(--text)]"})}),n===e.length-1&&(0,s.jsx)(l.$,{"aria-label":"Reload",variant:"message-action",size:"xs",onClick:a,title:"Reload last prompt",children:(0,s.jsx)(r.jEl,{className:"text-[var(--text)]"})})]}),(0,s.jsx)(y,{turn:t,index:n,isEditing:m===n,editText:p,onStartEdit:j,onSetEditText:g,onSaveEdit:C,onCancelEdit:N}),"user"===t.role&&(0,s.jsx)("div",{className:(0,c.cn)("flex flex-col items-center self-end space-y-0 ml-0 pb-1 transition-opacity duration-100",d===n?"opacity-100 pointer-events-auto":"opacity-0 pointer-events-none"),children:m!==n&&(0,s.jsx)(l.$,{"aria-label":"Copy",variant:"message-action",size:"sm",onClick:()=>w(t.rawContent),title:"Copy message",children:(0,s.jsx)(r.nxz,{className:"text-[var(--text)]"})})})]},t.timestamp||`turn_${n}`))),(0,s.jsx)("div",{ref:v,style:{height:"1px"}})]})}},8698:(e,t,a)=>{a.d(t,{N:()=>r});var s=a(6540),n=a(6948);const r=()=>{const{config:e,updateConfig:t}=(0,n.UK)(),a=(0,s.useRef)(0),r=[{host:"ollama",isEnabled:e=>!!e.ollamaUrl&&!0===e.ollamaConnected,getUrl:e=>`${e.ollamaUrl}/api/tags`,parseFn:(e,t)=>(e?.models??[]).map((e=>({...e,id:e.id??e.name,host:t}))),onFetchFail:(e,t)=>t({ollamaConnected:!1,ollamaUrl:""})}];return{fetchAllModels:(0,s.useCallback)((async()=>{const s=Date.now();if(s-a.current<3e4)return void console.log("[useUpdateModels] Model fetch throttled");a.current=s;const n=e;if(!n)return void console.warn("[useUpdateModels] Config not available, skipping fetch.");console.log("[useUpdateModels] Starting model fetch for all configured services...");const o=await Promise.allSettled(r.map((async e=>{if(!e.isEnabled(n))return{host:e.host,models:[],status:"disabled"};const a=e.getUrl(n);if(!a)return console.warn(`[useUpdateModels] Could not determine URL for host: ${e.host}`),{host:e.host,models:[],status:"error",error:"Invalid URL"};const s=e.getFetchOptions?e.getFetchOptions(n):{},r=await(async(e,t={})=>{try{const a=await fetch(e,t);return a.ok?await a.json():void console.error(`[fetchDataSilently] HTTP error! Status: ${a.status} for URL: ${e}`)}catch(t){return void console.error(`[fetchDataSilently] Fetch or JSON parse error for URL: ${e}`,t)}})(a,s);if(r){const t=e.parseFn(r,e.host);return{host:e.host,models:t,status:"success"}}return e.onFetchFail&&e.onFetchFail(n,t),{host:e.host,models:[],status:"error",error:"Fetch failed"}})));let i=[];o.forEach((e=>{"fulfilled"===e.status&&"success"===e.value.status&&i.push(...e.value.models)}));const l=n.models||[],c={};((e,t)=>{if(e.length!==t.length)return!0;const a=(e,t)=>e.id.localeCompare(t.id),s=[...e].sort(a),n=[...t].sort(a);return JSON.stringify(s)!==JSON.stringify(n)})(i,l)&&(console.log("[useUpdateModels] Aggregated models changed. Updating config."),c.models=i);const d=n.selectedModel,u=c.models||l,m=d&&u.some((e=>e.id===d))?d:u[0]?.id;(m!==d||c.models)&&(c.selectedModel=m),Object.keys(c).length>0?t(c):console.log("[useUpdateModels] No changes to models or selectedModel needed."),console.log("[useUpdateModels] Model fetch cycle complete.")}),[e,t,3e4,r])}}},8971:(e,t,a)=>{a.d(t,{A:()=>l});var s=a(6540),n=a(2951),r=a(5431);const o=1e3;var i=a(1100);try{const e=chrome.runtime.getURL("pdf.worker.mjs");e?i.EA.workerSrc=e:console.error("Failed to get URL for pdf.worker.mjs. PDF parsing might fail.")}catch(e){console.error("Error setting pdf.js worker source:",e)}const l=(e,t,a,l,c,d,u,m,h,p,g)=>{const f=(0,s.useRef)(null),x=(0,s.useRef)(null),v=(e,t,a,s,n)=>{if(f.current===e||a||s||n){if(null===f.current&&null!==e&&(""===t&&a&&!s&&!n||s&&(t.includes("Operation cancelled by user")||t.includes("Streaming operation cancelled"))))return console.log(`[${e}] updateAssistantTurn: Signal received after operation already finalized. Preserving existing state.`),p(!1),void g("idle");d((r=>{if(0===r.length||"assistant"!==r[r.length-1].role){if(console.warn(`[${e}] updateAssistantTurn: No assistant turn found or last turn is not assistant.`),s){const e={role:"assistant",rawContent:`Error: ${t||"Unknown operation error"}`,status:"error",timestamp:Date.now()};return[...r,e]}return r}const o=r[r.length-1],i=!0===s?"error":!0===n?"cancelled":a?"complete":"streaming";let l;if(n){const e=o.rawContent||"";l=e+(e?" ":"")+t}else l=s?`Error: ${t||"Unknown stream/handler error"}`:t;return[...r.slice(0,-1),{...o,rawContent:l,status:i,timestamp:Date.now()}]})),(a||!0===s||!0===n)&&(console.log(`[${e}] updateAssistantTurn: Final state (Finished: ${a}, Error: ${s}, Cancelled: ${n}). Clearing guard and loading.`),p(!1),g(s||n?"idle":"done"),f.current===e&&(f.current=null,x.current&&(x.current=null)))}else console.log(`[${e}] updateAssistantTurn: Guard mismatch (current: ${f.current}), skipping non-final update.`)};return{onSend:async t=>{const s=Date.now();console.log(`[${s}] useSendMessage: onSend triggered.`);const l=t||"";if(!c)return console.log(`[${s}] useSendMessage: Bailing out: Missing config.`),void p(!1);if(!l||!c)return void console.log(`[${s}] useSendMessage: Bailing out: Missing message or config.`);null!==f.current&&(console.warn(`[${s}] useSendMessage: Another send operation (ID: ${f.current}) is already in progress. Aborting previous.`),x.current&&x.current.abort());const b=new AbortController;x.current=b,console.log(`[${s}] useSendMessage: Setting loading true.`),p(!0),m(""),h("");const y=c.chatMode||"chat";g("web"===y?"searching":"page"===y?"reading":"thinking"),f.current=s;const w=l.match(/(https?:\/\/[^\s]+)/g);let j="";if(w&&w.length>0){g("searching");try{j=(await Promise.all(w.map((e=>(0,n.hj)(e,b.signal))))).map(((e,t)=>`Content from [${w[t]}]:\n${e}`)).join("\n\n")}catch(e){j="[Error scraping one or more URLs]"}g("thinking")}const N={role:"user",status:"complete",rawContent:l,timestamp:Date.now()};d((e=>[...e,N])),u(""),console.log(`[${s}] useSendMessage: User turn added to state.`);const C={role:"assistant",rawContent:"",status:"streaming",timestamp:Date.now()+1};d((e=>[...e,C])),console.log(`[${s}] useSendMessage: Assistant placeholder turn added early.`);let S=l,k="",$="";const M=c?.models?.find((e=>e.id===c.selectedModel));if(!M)return console.error(`[${s}] useSendMessage: No current model found.`),void v(s,"Configuration error: No model selected.",!0,!0);const A=void 0;{console.log(`[${s}] useSendMessage: Optimizing query...`),g("thinking");const e=a.map((e=>({role:e.role,content:e.rawContent})));try{const t=await(0,n.GW)(l,c,M,A,b.signal,e);t&&t.trim()&&t!==l?(S=t,$=`**Optimized query:** "*${S}*"\n\n`,console.log(`[${s}] useSendMessage: Query optimized to: "${S}"`)):($=`**Original query:** "${S}"\n\n`,console.log(`[${s}] useSendMessage: Using original query: "${S}"`))}catch(e){console.error(`[${s}] Query optimization failed:`,e),$=`**Fallback query:** "${S}"\n\n`}}console.log(`[${s}] useSendMessage: Performing web search...`),g("searching");try{if(k=await(0,n.tE)(S,c,b.signal),g("thinking"),b.signal.aborted)return void console.log(`[${s}] Web search was aborted (signal check post-await).`)}catch(e){if(console.error(`[${s}] Web search failed:`,e),"AbortError"===e.name||b.signal.aborted)return void console.log(`[${s}] Web search aborted. onStop handler will finalize UI.`);{k="";const t=`Web Search Failed: ${e instanceof Error?e.message:String(e)}`;return g("idle"),void v(s,t,!0,!0,!1)}}console.log(`[${s}] useSendMessage: Web search completed. Length: ${k.length}`),$&&d((e=>e.map((t=>"assistant"===t.role&&e[e.length-1]===t&&"complete"!==t.status&&"error"!==t.status&&"cancelled"!==t.status?{...t,webDisplayContent:$}:t))));const E=S,T=1e3*(c?.webLimit||1),z=T&&"string"==typeof k?k.substring(0,T):k,P=128===c?.webLimit?k:z,L=a.map((e=>({content:e.rawContent||"",role:e.role}))).concat({role:"user",content:l});let _="";if("page"===c?.chatMode){let e="";console.log(`[${s}] useSendMessage: Preparing page content...`),g("reading");try{const[t]=await chrome.tabs.query({active:!0,lastFocusedWindow:!0});if(t?.url&&!t.url.startsWith("chrome://")){const a=t.url,n=t.mimeType;if(a.toLowerCase().endsWith(".pdf")||n&&"application/pdf"===n){console.log(`[${s}] Detected PDF URL: ${a}. Attempting to extract text.`);try{e=await async function(e,t){try{console.log(`[${t||"PDF"}] Attempting to fetch PDF from URL: ${e}`);const a=await fetch(e);if(!a.ok)throw new Error(`Failed to fetch PDF: ${a.status} ${a.statusText}`);const s=await a.arrayBuffer();console.log(`[${t||"PDF"}] PDF fetched, size: ${s.byteLength} bytes. Parsing...`);const n=await i.YE({data:s}).promise;console.log(`[${t||"PDF"}] PDF parsed. Number of pages: ${n.numPages}`);let r="";for(let e=1;e<=n.numPages;e++){const a=await n.getPage(e);r+=(await a.getTextContent()).items.map((e=>"str"in e?e.str:"")).join(" ")+"\n\n",e%10!=0&&e!==n.numPages||console.log(`[${t||"PDF"}] Extracted text from page ${e}/${n.numPages}`)}return console.log(`[${t||"PDF"}] PDF text extraction complete. Total length: ${r.length}`),r.trim()}catch(a){throw console.error(`[${t||"PDF"}] Error extracting text from PDF (${e}):`,a),a}}(a,s),console.log(`[${s}] Successfully extracted text from PDF. Length: ${e.length}`)}catch(t){console.error(`[${s}] Failed to extract text from PDF ${a}:`,t),e=`Error extracting PDF content: ${t instanceof Error?t.message:"Unknown PDF error"}. Falling back.`}}else console.log(`[${s}] URL is not a PDF. Fetching from storage: ${a}`),e=await r.A.getItem("pagestring")||"",console.log(`[${s}] Retrieved page text content from storage. Length: ${e.length}`)}else console.log(`[${s}] Not fetching page content for URL: ${t?.url} (might be chrome:// or no active tab).`)}catch(t){console.error(`[${s}] Error getting active tab or initial page processing:`,t),e=`Error accessing page content: ${t instanceof Error?t.message:"Unknown error"}`}const t=1e3*(c?.contextLimit||1),a="string"==typeof e?e:"",n=t&&a?a.substring(0,t):a;_=128===c?.contextLimit?a:n,h(_||""),g("thinking"),console.log(`[${s}] Page content prepared for LLM. Length: ${_?.length}`)}else h("");const R=c?.personas?.[c?.persona]||"",O="page"===c?.chatMode&&_?`Use the following page content for context: ${_}`:"",I=P?`Refer to this web search summary: ${P}`:"",D=c?.useNote&&c.noteContent?`Refer to this note for context: ${c.noteContent}`:"";let U="";const F=c.userName?.trim(),q=c.userProfile?.trim();F&&"user"!==F.toLowerCase()&&""!==F?(U=`You are interacting with a user named "${F}".`,q&&(U+=` Their provided profile information is: "${q}".`)):q&&(U=`You are interacting with a user. Their provided profile information is: "${q}".`);const W=[];R&&W.push(R),U&&W.push(U),D&&W.push(D),O&&W.push(O),I&&W.push(I),j&&W.push(`Use the following scraped content from URLs in the user's message:\n${j}`);const B=W.join("\n\n").trim();console.log(`[${s}] useSendMessage: System prompt constructed. Persona: ${!!R}, UserCtx: ${!!U}, NoteCtx: ${!!D}, PageCtx: ${!!O}, WebCtx: ${!!I}, LinkCtx: ${!!j}`);try{if(g("thinking"),"high"===c?.computeLevel&&M)console.log(`[${s}] useSendMessage: Starting HIGH compute level.`),await(async(e,t,a,s,r,i,l)=>{const c=Math.max(.1,.5*(a.temperature||.7)),d=()=>{if(l.aborted)throw new Error("Operation cancelled by user")};i("Decomposing task into stages...",!1),d(),await new Promise((e=>setTimeout(e,o)));const u=`You are a planning agent. Given the original task: "${e}", break it down into the main sequential stages required to accomplish it. Output *only* a numbered list of stages. Example:\n1. First stage\n2. Second stage`,m=await(0,n.GW)(u,a,s,r,l,[],c),h=m.split("\n").map((e=>e.trim())).filter((e=>e.match(/^\d+\./)));if(console.log("HighCompute - Raw L1 Decomposition Result:",m),i(`Monitoring: Generated Stages:\n${h.join("\n")||"[None]"}`,!1),d(),!h||0===h.length)return i("Error: Failed to decompose task into stages. Falling back to direct query.",!0),"Error: Could not decompose task.";const p=[];for(let t=0;t<h.length;t++){const u=h[t];d(),i(`Processing Stage ${t+1}/${h.length}: ${u}...`,!1);const m=`You are a planning agent. Given the stage: "${u}", break it down into the specific sequential steps needed to complete it. Output *only* a numbered list of steps. If no further breakdown is needed, output "No breakdown needed."`;d(),await new Promise((e=>setTimeout(e,o)));const g=await(0,n.GW)(m,a,s,r,l,[],c);console.log(`HighCompute - Raw L2 Decomposition Result (Stage ${t+1}):`,g);const f=g.split("\n").map((e=>e.trim())).filter((e=>e.match(/^\d+\./)));i(`Monitoring: Generated Steps for Stage ${t+1}:\n${f.join("\n")||"[None, or direct solve]"}`,!1);let x="";if(0===f.length||g.includes("No breakdown needed")){i(`Solving Stage ${t+1} directly...`,!1),d();const c=`Complete the following stage based on the original task "${e}": "${u}"`;d(),await new Promise((e=>setTimeout(e,o))),x=await(0,n.GW)(c,a,s,r,l),console.log(`HighCompute - Raw Direct Solve Result (Stage ${t+1}):`,x),i(`Monitoring: Direct Solve Result for Stage ${t+1}:\n${x}`,!1)}else{const m=[],h=2;let p="";for(let c=0;c<f.length;c+=h){const g=f.slice(c,c+h),x=c/h+1;d(),i(`Solving Step Batch ${x} for Stage ${t+1}: ${g.join(", ")}...`,!1);const v=`You are an expert problem solver. Given the stage: "${u}" and the original task: "${e}", complete the following steps.  Consider the following accumulated context from previous steps: ${p}\n\n${g.map(((e,t)=>`${c+t+1}. ${e}`)).join("\n")}\n\nProvide your answer in the same numbered format as the steps.`;d(),await new Promise((e=>setTimeout(e,o)));const b=await(0,n.GW)(v,a,s,r,l);console.log(`HighCompute - Raw Batch Results (Stage ${t+1}, Batch ${x}):`,b),i(`Monitoring: Raw Batch Results for Stage ${t+1}, Batch ${x}:\n${b}`,!1);const y=b.split("\n").map((e=>e.trim())).filter((e=>e.match(/^\d+\./))).map((e=>e.replace(/^\d+\.\s*/,"")));console.log(`HighCompute - Parsed Batch Results (Stage ${t+1}, Batch ${x}):`,y),i(`Monitoring: Parsed Batch Results for Stage ${t+1}, Batch ${x}:\n${y.join("\n")||"[None]"}`,!1);for(let e=0;e<y.length;e++){const t=y[e];m.push(t),p+=`Step ${c+e+1}: ${t}\n`}}i(`Synthesizing results for Stage ${t+1}...`,!1),d(),await new Promise((e=>setTimeout(e,o)));const g=`Synthesize the results of the following steps for stage "${u}" into a coherent paragraph:\n\n${m.map(((e,t)=>`Step ${t+1} Result:\n${e}`)).join("\n\n")}`;x=await(0,n.GW)(g,a,s,r,l,[],c),console.log(`HighCompute - Raw Stage Synthesis Result (Stage ${t+1}):`,x),i(`Monitoring: Synthesized Result for Stage ${t+1}:\n${x}`,!1)}p.push(x),i(`Monitoring: Accumulated Stage Results so far:\n${p.map(((e,t)=>`Stage ${t+1}: ${e}`)).join("\n---\n")}`,!1)}i("Synthesizing final answer...",!1),d();const g=`Based on the results of the following stages, provide a final comprehensive answer for the original task "${e}":\n\n${p.map(((e,t)=>`Stage ${t+1} (${h[t]}):\n${e}`)).join("\n\n")}`;i(`Monitoring: Final Synthesis Prompt:\n${g}`,!1),console.log("HighCompute - Final Synthesis Prompt:",g),d(),await new Promise((e=>setTimeout(e,o)));const f=await(0,n.GW)(g,a,s,r,l,[],c);console.log("HighCompute - Raw Final Synthesis Result:",f);const x="**High Compute Breakdown:**\n\n"+p.map(((e,t)=>`**Stage ${t+1}: ${h[t]}**\n${e}`)).join("\n\n---\n\n")+`\n\n---\n**Final Synthesized Answer:**\n${f}`;return i(x,!0),x})(E,0,c,M,A,((e,t)=>v(s,e,Boolean(t))),b.signal),console.log(`[${s}] useSendMessage: HIGH compute level finished.`);else if("medium"===c?.computeLevel&&M)console.log(`[${s}] useSendMessage: Starting MEDIUM compute level.`),await(async(e,t,a,s,r,i,l)=>{const c=Math.max(.1,.5*(a.temperature||.7)),d=()=>{if(l.aborted)throw new Error("Operation cancelled by user")};i("Decomposing task into subtasks...",!1),d(),await new Promise((e=>setTimeout(e,o)));const u=`You are a planning agent. Given the task: "${e}", break it down into logical subtasks needed to accomplish it. Output *only* a numbered list of subtasks.`,m=await(0,n.GW)(u,a,s,r,l,[],c),h=m.split("\n").map((e=>e.trim())).filter((e=>e.match(/^\d+\./)));if(console.log("MediumCompute - Raw Decomposition Result:",m),i(`Monitoring: Generated Subtasks:\n${h.join("\n")||"[None]"}`,!1),d(),!h||0===h.length){i("Warning: Failed to decompose into subtasks. Attempting direct query.",!1),d(),await new Promise((e=>setTimeout(e,o)));const t=await(0,n.GW)(e,a,s,r,l);return i(t,!0),t}const p=[];for(let t=0;t<h.length;t+=2){const c=h.slice(t,t+2),u=t/2+1;d(),i(`Solving Subtask Batch ${u}: ${c.join(", ")}...`,!1);const m=`You are an expert problem solver. Given the task: "${e}", complete the following subtasks:\n\n${c.map(((e,a)=>`${t+a+1}. ${e}`)).join("\n")}\n\nProvide your answer in the same numbered format as the subtasks.`;d(),await new Promise((e=>setTimeout(e,o)));const g=await(0,n.GW)(m,a,s,r,l);console.log(`MediumCompute - Raw Batch Results (Batch ${u}):`,g),i(`Monitoring: Raw Batch Results for Batch ${u}:\n${g}`,!1);const f=g.split("\n").map((e=>e.trim())).filter((e=>e.match(/^\d+\./))).map((e=>e.replace(/^\d+\.\s*/,"")));console.log(`MediumCompute - Parsed Batch Results (Batch ${u}):`,f),i(`Monitoring: Parsed Batch Results for Batch ${u}:\n${f.join("\n")||"[None]"}`,!1);for(let e=0;e<f.length;e++)p.push(f[e])}i("Synthesizing final answer...",!1),d(),await new Promise((e=>setTimeout(e,o)));const g=`Synthesize the results of the following subtasks into a final comprehensive answer for the original task "${e}":\n\n${p.map(((e,t)=>`Subtask ${t+1} Result:\n${e}`)).join("\n\n")}`;console.log("MediumCompute - Final Synthesis Prompt:",g),i(`Monitoring: Final Synthesis Prompt:\n${g}`,!1);const f=await(0,n.GW)(g,a,s,r,l,[],c);console.log("MediumCompute - Raw Final Synthesis Result:",f);const x="**Medium Compute Breakdown:**\n\n"+p.map(((e,t)=>`**Subtask ${t+1}: ${h[t]}**\n${e}`)).join("\n\n---\n\n")+`\n\n---\n**Final Synthesized Answer:**\n${f}`;return i(x,!0),x})(E,0,c,M,A,((e,t)=>v(s,e,Boolean(t))),b.signal),console.log(`[${s}] useSendMessage: MEDIUM compute level finished.`);else{console.log(`[${s}] useSendMessage: Starting standard streaming.`);const e={stream:!0},t={ollama:`${c?.ollamaUrl||""}/api/chat`}[M.host||""];if(!t)return void v(s,`Configuration error: Could not determine API URL for host '${M.host}'.`,!0,!0);const a=[];""!==B.trim()&&a.push({role:"system",content:B}),a.push(...L),console.log(`[${s}] useSendMessage: Sending chat request to ${t} with system prompt: "${B}"`),await(0,n.hL)(t,{...e,model:c?.selectedModel||"",messages:a,temperature:c?.temperature??.7,max_tokens:c?.maxTokens??32048,top_p:c?.topP??1,presence_penalty:c?.presencepenalty??0},((e,t,a)=>{v(s,e,Boolean(t),Boolean(a)),(t||a)&&console.log(`[${s}] fetchDataAsStream Callback: Stream finished/errored.`)}),A,M.host||"",b.signal),console.log(`[${s}] useSendMessage: fetchDataAsStream call INITIATED.`)}}catch(t){if(b.signal.aborted)console.log(`[${s}] Send operation was aborted. 'onStop' handler is responsible for UI updates.`),e&&p(!1),g("idle"),f.current===s&&(f.current=null),x.current&&x.current.signal===b.signal&&(x.current=null);else{console.error(`[${s}] useSendMessage: Error during send operation:`,t);const e=t instanceof Error?t.message:String(t);v(s,e,!0,!0)}}console.log(`[${s}] useSendMessage: onSend processing logic completed.`)},onStop:()=>{const e=f.current;null!==e?(console.log(`[${e}] useSendMessage: onStop triggered.`),x.current&&(x.current.abort(),x.current=null),v(e,"[Operation cancelled by user]",!0,!1,!0)):(console.log("[No CallID] useSendMessage: onStop triggered but no operation in progress."),p(!1),g("idle"))}}}},9018:(e,t,a)=>{a.d(t,{bq:()=>p,eb:()=>f,gC:()=>g,l6:()=>m,yv:()=>h});var s=a(4848),n=a(6540),r=a(854),o=a(5107),i=a(5773),l=a(2102),c=a(5284);const d={default:"bg-transparent data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive shadow-xs data-[size=sm]:h-8",settingsPanel:(0,c.cn)("text-[var(--text)] rounded-xl shadow-md","focus:border-[var(--active)] focus:ring-1 focus:ring-[var(--active)]","hover:border-[var(--active)] hover:brightness-98","bg-[var(--bg)] border border-[var(--text)]/20","backdrop-blur-sm","h-8"),settings:(0,c.cn)("text-[var(--text)] rounded-md shadow-md","focus:border-[var(--active)] focus:ring-1 focus:ring-[var(--active)]","hover:border-[var(--active)] hover:brightness-98","bg-[var(--bg)] border border-[var(--text)]/20","backdrop-blur-sm","h-8")},u={default:"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md",settingsPanel:(0,c.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto","bg-[var(--bg)] text-[var(--text)] border border-[var(--text)]/10","rounded-xl shadow-lg backdrop-blur-sm")};function m({...e}){return(0,s.jsx)(r.bL,{"data-slot":"select",...e})}function h({...e}){return(0,s.jsx)(r.WT,{"data-slot":"select-value",...e})}const p=n.forwardRef((({className:e,size:t="default",variant:a="default",children:n,...i},l)=>(0,s.jsxs)(r.l9,{ref:l,"data-slot":"select-trigger","data-size":t,className:(0,c.cn)("flex w-fit items-center justify-between gap-2 rounded-md border px-3 py-2 text-sm whitespace-nowrap transition-[color,box-shadow] outline-none disabled:cursor-not-allowed disabled:opacity-50 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",d[a],e),...i,children:[n,(0,s.jsx)(r.In,{asChild:!0,children:(0,s.jsx)(o.A,{className:"size-4 opacity-50"})})]})));p.displayName=r.l9.displayName;const g=n.forwardRef((({className:e,children:t,position:a="popper",variant:n="default",...o},i)=>(0,s.jsx)(r.ZL,{children:(0,s.jsxs)(r.UC,{ref:i,"data-slot":"select-content",className:(0,c.cn)(u[n],"default"===n&&"popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:a,...o,children:[(0,s.jsx)(x,{}),(0,s.jsx)(r.LM,{className:(0,c.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,s.jsx)(v,{})]})})));function f({className:e,children:t,focusVariant:a="default",...n}){return(0,s.jsxs)(r.q7,{"data-slot":"select-item",className:(0,c.cn)("[&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2","activeTheme"===a?"text-[var(--text)] hover:bg-[var(--active)]/10 focus:bg-[var(--active)]/20 focus:text-[var(--text)] data-[highlighted]:bg-[var(--active)]/15":"text-popover-foreground focus:bg-accent focus:text-accent-foreground",e),...n,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(r.VF,{children:(0,s.jsx)(i.A,{className:"size-4"})})}),(0,s.jsx)(r.p4,{children:t})]})}function x({className:e,...t}){return(0,s.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(l.A,{className:"size-4"})})}function v({className:e,...t}){return(0,s.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(o.A,{className:"size-4"})})}g.displayName=r.UC.displayName},9696:(e,t,a)=>{a.d(t,{T:()=>o});var s=a(4848),n=(a(6540),a(1663)),r=a(5284);function o({className:e,autosize:t=!1,minRows:a,maxRows:o,style:i,...l}){return t?(0,s.jsx)(n.A,{minRows:a,maxRows:o,style:i,className:(0,r.cn)("flex w-full bg-transparent placeholder:text-muted-foreground","focus-visible:border-ring focus-visible:ring-ring/50","field-sizing-content text-sm md:text-sm transition-[color,box-shadow] outline-none focus-visible:ring-[3px]","disabled:cursor-not-allowed disabled:opacity-50","thin-scrollbar",e),...l}):(0,s.jsx)("textarea",{"data-slot":"textarea-default",className:(0,r.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-sm shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...l})}},9828:(e,t,a)=>{a.a(e,(async(e,s)=>{try{a.d(t,{A:()=>O});var n=a(4848),r=a(6540),o=a(888),i=a(3790),l=a.n(i),c=a(5066),d=a(1735),u=a(9197),m=a(2090),h=a(3885),p=a(5284),g=a(9853),f=a(8971),x=a(8698),v=a(523),b=a(8473),y=a(6948),w=a(3108),j=a(4339),N=a(8639),C=a(7660),S=a(3842),k=a(5431),$=a(6174),M=a(5095),A=a(1979),E=e([C]);function T(){let e="",t="",a="",s="",n="",r="",o="";try{e=document.title||"";const i=5e6;if(document.body,document.body&&document.body.innerHTML.length>i){console.warn(`[ChromePanion Bridge] Document body is very large (${document.body.innerHTML.length} chars). Attempting to use a cloned, simplified version for text extraction to improve performance/stability.`);const e=document.body.cloneNode(!0);e.querySelectorAll("script, style, noscript, iframe, embed, object").forEach((e=>e.remove())),t=(e.textContent||"").replace(/\s\s+/g," ").trim(),a=document.body.innerHTML.replace(/\s\s+/g," ")}else document.body?(t=(document.body.innerText||"").replace(/\s\s+/g," ").trim(),a=(document.body.innerHTML||"").replace(/\s\s+/g," ")):console.warn("[ChromePanion Bridge] document.body is not available.");s=Array.from(document.images).map((e=>e.alt)).filter((e=>e&&e.trim().length>0)).join(". "),n=Array.from(document.querySelectorAll("table")).map((e=>(e.innerText||"").replace(/\s\s+/g," "))).join("\n");const l=document.querySelector('meta[name="description"]');r=l&&l.getAttribute("content")||"";const c=document.querySelector('meta[name="keywords"]');o=c&&c.getAttribute("content")||""}catch(e){console.error("[ChromePanion Bridge] Error during content extraction:",e);let t="Unknown extraction error";return e instanceof Error?t=e.message:"string"==typeof e&&(t=e),JSON.stringify({error:`Extraction failed: ${t}`,title:document.title||"Error extracting title",text:"",html:"",altTexts:"",tableData:"",meta:{description:"",keywords:""}})}const i=1e7;let l={title:e,text:t,html:a,altTexts:s,tableData:n,meta:{description:r,keywords:o}};if(JSON.stringify(l).length>i){console.warn("[ChromePanion Bridge] Total extracted content is very large. Attempting to truncate.");const e=i-JSON.stringify({...l,text:"",html:""}).length;let t=e;l.text.length>.6*t&&(l.text=l.text.substring(0,Math.floor(.6*t))+"... (truncated)"),t=e-l.text.length,l.html.length>.8*t&&(l.html=l.html.substring(0,Math.floor(.8*t))+"... (truncated)"),console.warn("[ChromePanion Bridge] Content truncated. Final approx length:",JSON.stringify(l).length)}return JSON.stringify(l)}async function z(){const[e]=await chrome.tabs.query({active:!0,lastFocusedWindow:!0});if(!e?.id||e.url?.startsWith("chrome://")||e.url?.startsWith("chrome-extension://")||e.url?.startsWith("about:"))return k.A.deleteItem("pagestring"),k.A.deleteItem("pagehtml"),k.A.deleteItem("alttexts"),void k.A.deleteItem("tabledata");k.A.deleteItem("pagestring"),k.A.deleteItem("pagehtml"),k.A.deleteItem("alttexts"),k.A.deleteItem("tabledata");try{const t=await chrome.scripting.executeScript({target:{tabId:e.id},func:T});if(!t||!Array.isArray(t)||0===t.length||!t[0]||"string"!=typeof t[0].result)return void console.error("[ChromePanion:] Bridge function execution returned invalid or unexpected results structure:",t);const a=t[0].result;let s;try{s=JSON.parse(a)}catch(e){return void console.error("[ChromePanion:] Failed to parse JSON result from bridge:",e,"Raw result string:",a)}if(s.error)return void console.error("[ChromePanion:] Bridge function reported an error:",s.error,"Title:",s.title);try{k.A.setItem("pagestring",s?.text??""),k.A.setItem("pagehtml",s?.html??""),k.A.setItem("alttexts",s?.altTexts??""),k.A.setItem("tabledata",s?.tableData??"")}catch(e){console.error("[ChromePanion:] Storage error after successful extraction:",e),k.A.deleteItem("pagestring"),k.A.deleteItem("pagehtml"),k.A.deleteItem("alttexts"),k.A.deleteItem("tabledata")}}catch(e){console.error("[ChromePanion:] Bridge function execution failed:",e),e instanceof Error&&(e.message.includes('Cannot access contents of url "chrome://')||e.message.includes("Cannot access a chrome extension URL")||e.message.includes('Cannot access contents of url "about:'))&&console.warn("[ChromePanion:] Cannot access restricted URL.")}}C=(E.then?(await E)():E)[0];const P=()=>`chat_${Math.random().toString(16).slice(2)}`,L=({children:e,onClick:t})=>(0,n.jsx)("div",{className:(0,p.cn)("bg-[var(--active)] border border-[var(--text)] rounded-[16px] text-[var(--text)]","cursor-pointer flex items-center justify-center","text-md font-extrabold p-0.5 place-items-center relative text-center","w-16 flex-shrink-0","transition-colors duration-200 ease-in-out","hover:bg-[rgba(var(--text-rgb),0.1)]"),onClick:t,children:e}),_=[{id:"Google",icon:u.DSS,label:"Google Search"}],R=({children:e,onClick:t,isActive:a,title:s})=>(0,n.jsxs)(h.m_,{children:[(0,n.jsx)(h.k$,{children:(0,n.jsx)("div",{className:(0,p.cn)("border rounded-lg text-[var(--text)]","cursor-pointer flex items-center justify-center","p-2 place-items-center relative","w-8 h-8 flex-shrink-0","transition-colors duration-200 ease-in-out",a?"bg-[var(--active)] text-[var(--text)] border-[var(--active)] hover:brightness-95":"bg-transparent border-[var(--text)]/50 hover:bg-[rgba(var(--text-rgb),0.1)]"),onClick:t,"aria-label":s,children:e})}),(0,n.jsx)(h.ZI,{side:"top",className:"bg-[var(--active)]/80 text-[var(--text)] border-[var(--text)]/50",children:(0,n.jsx)("p",{children:s})})]}),O=()=>{const[e,t]=(0,r.useState)([]),[a,s]=(0,r.useState)(""),[i,u]=(0,r.useState)(P()),[E,T]=(0,r.useState)(""),[O,I]=(0,r.useState)(""),[D,U]=(0,r.useState)(!1),[F,q]=(0,r.useState)(!1),[W,B]=(0,r.useState)(!1),{config:G,updateConfig:V}=(0,y.UK)(),[H,Y]=(0,r.useState)(!1),[K,J]=(0,r.useState)({id:null,url:""}),Z=(0,r.useRef)(null),Q=(0,r.useRef)({id:null,url:""}),[X,ee]=(0,r.useState)(!1),[te,ae]=(0,r.useState)(!1),[se,ne]=(0,r.useState)("idle"),[re,oe]=(0,r.useState)(!1);(0,r.useRef)(null),(0,r.useEffect)((()=>{const e=new ResizeObserver((()=>{Z.current&&(Z.current.style.minHeight="100dvh",requestAnimationFrame((()=>{Z.current&&(Z.current.style.minHeight="")})))}));return Z.current&&e.observe(Z.current),()=>e.disconnect()}),[]),(0,r.useEffect)((()=>{if("page"!==G?.chatMode)return;const e=async()=>{const[e]=await chrome.tabs.query({active:!0,lastFocusedWindow:!0});if(e?.id&&e.url)return e.url.startsWith("chrome://")||e.url.startsWith("chrome-extension://")||e.url.startsWith("about:")?(Q.current.id===e.id&&Q.current.url===e.url||(k.A.deleteItem("pagestring"),k.A.deleteItem("pagehtml"),k.A.deleteItem("alttexts"),k.A.deleteItem("tabledata")),Q.current={id:e.id,url:e.url},void J({id:e.id,url:e.url})):void(e.id===Q.current.id&&e.url===Q.current.url||(Q.current={id:e.id,url:e.url},J({id:e.id,url:e.url}),await z()))};e();const t=t=>{chrome.tabs.get(t.tabId,(t=>{chrome.runtime.lastError?console.warn(`[ChromePanion ] Error getting tab info on activation: ${chrome.runtime.lastError.message}`):e()}))},a=(t,a,s)=>{s.active&&("complete"===a.status||a.url&&"complete"===s.status)&&e()};return chrome.tabs.onActivated.addListener(t),chrome.tabs.onUpdated.addListener(a),()=>{chrome.tabs.onActivated.removeListener(t),chrome.tabs.onUpdated.removeListener(a),Q.current={id:null,url:""}}}),[G?.chatMode]),(0,r.useEffect)((()=>{const e=e=>{};return document.addEventListener("keydown",e),()=>{document.removeEventListener("keydown",e)}}),[G?.chatMode,V,F,W]),(0,r.useEffect)((()=>{const e=(e,t,a)=>"ACTIVATE_NOTE_SYSTEM_VIEW"===e.type&&(console.log("[ChromePanion.tsx] Received ACTIVATE_NOTE_SYSTEM_VIEW. Switching to Note System mode."),q(!1),B(!1),Y(!0),a({status:"ACTIVATING_NOTE_SYSTEM_VIEW_ACK"}),!0);return chrome.runtime.onMessage.addListener(e),()=>{chrome.runtime.onMessage.removeListener(e)}}),[q,B,Y]);const{appendToNote:ie}=(0,M.e)();(0,r.useEffect)((()=>{const e=chrome.runtime.connect({name:$.A.SidePanelPort}),t=e=>{"ADD_SELECTION_TO_NOTE"===e.type&&e.payload&&ie(e.payload)};return e.onMessage.addListener(t),e.postMessage({type:"init"}),()=>{e.onMessage.removeListener(t),e.disconnect()}}),[ie]);const{chatTitle:le,setChatTitle:ce}=(0,g.S)(D,e,a),{onSend:de,onStop:ue}=(0,f.A)(D,a,e,E,G,t,s,T,I,U,ne);(0,x.N)();const me=()=>{t([]),I(""),T(""),U(!1),V({chatMode:"web",computeLevel:"low"}),ne("idle"),s(""),ce(""),u(P()),B(!1),q(!1),Y(!1),Z.current&&(Z.current.scrollTop=0)},he=async()=>{try{const t=(await l().keys()).filter((e=>e.startsWith("chat_")));if(0===t.length&&0===e.length)return;await Promise.all(t.map((e=>l().removeItem(e)))),o.oR.success("Deleted all chats"),me()}catch(e){console.error("[ChromePanion] Error deleting all chats:",e),o.oR.error("Failed to delete chats")}};(0,r.useEffect)((()=>{if(e.length>0&&!W&&!F&&!H){const t={id:i,title:le||`Chat ${new Date(Date.now()).toLocaleString()}`,turns:e,last_updated:Date.now(),model:G?.selectedModel,chatMode:G?.chatMode,webMode:"web"===G?.chatMode?G.webMode:void 0,useNoteActive:G?.useNote,noteContentUsed:G?.useNote?G.noteContent:void 0};l().setItem(i,t).catch((e=>{console.error(`[ChromePanion ] Error saving chat ${i}:`,e)}))}}),[i,e,le,G?.selectedModel,G?.chatMode,G?.webMode,G?.useNote,G?.noteContent,W,F]),(0,r.useEffect)((()=>{if("done"===se||"idle"===se){const e=setTimeout((()=>{ne("idle")}),1500);return()=>clearTimeout(e)}}),[se]),(0,r.useEffect)((()=>{let e=!1;return(async()=>{if(!e){me();try{const[t]=await chrome.tabs.query({active:!0,lastFocusedWindow:!0});!e&&t?.id&&t.url?(J({id:t.id,url:t.url}),(t.url.startsWith("chrome://")||t.url.startsWith("chrome-extension://")||t.url.startsWith("about:"))&&(k.A.deleteItem("pagestring"),k.A.deleteItem("pagehtml"),k.A.deleteItem("alttexts"),k.A.deleteItem("tabledata"),Q.current={id:null,url:""})):e||(Q.current={id:null,url:""},J({id:null,url:""}),k.A.deleteItem("pagestring"),k.A.deleteItem("pagehtml"),k.A.deleteItem("alttexts"),k.A.deleteItem("tabledata"))}catch(t){e||(console.error("[ChromePanion - Revised] Error during panel open tab check:",t),Q.current={id:null,url:""},J({id:null,url:""}),k.A.deleteItem("pagestring"),k.A.deleteItem("pagehtml"),k.A.deleteItem("alttexts"),k.A.deleteItem("tabledata"))}}})(),()=>{e=!0,k.A.deleteItem("pagestring"),k.A.deleteItem("pagehtml"),k.A.deleteItem("alttexts"),k.A.deleteItem("tabledata"),me(),Q.current={id:null,url:""}}}),[]);const pe=(0,r.useCallback)((()=>{oe(!1)}),[]);return(0,n.jsx)(h.Bc,{delayDuration:300,children:(0,n.jsxs)("div",{ref:Z,className:(0,p.cn)("w-full h-dvh p-0 overflow-hidden","flex flex-col bg-[var(--bg)]"),children:[(0,n.jsx)(w.Y,{chatTitle:le,deleteAll:he,downloadImage:()=>(0,C.GV)(e),downloadJson:()=>(0,C.xD)(e),downloadText:()=>(0,C.mR)(e),downloadMarkdown:()=>(0,C.ii)(e),historyMode:W,reset:me,setHistoryMode:B,setSettingsMode:q,settingsMode:F,noteSystemMode:H,onAddNewNoteRequest:H?()=>oe(!0):void 0,setNoteSystemMode:Y,chatMode:G?.chatMode||"chat",chatStatus:se}),(0,n.jsxs)("div",{className:"flex flex-col flex-1 min-h-0 no-scrollbar overflow-y-auto relative",children:[F&&(0,n.jsx)(S.w,{}),!F&&W&&!H&&(0,n.jsx)(b.D,{className:"flex-1 w-full min-h-0",loadChat:e=>{ce(e.title||""),t(e.turns),u(e.id),B(!1),ne("idle"),q(!1);const a={useNote:e.useNoteActive??!1,noteContent:e.noteContentUsed||""};V(a),"page"!==a.chatMode&&(k.A.deleteItem("pagestring"),k.A.deleteItem("pagehtml"),k.A.deleteItem("alttexts"),k.A.deleteItem("tabledata"),Q.current={id:null,url:""})},onDeleteAll:he}),!F&&!W&&H&&(0,n.jsx)(A.z,{triggerOpenCreateModal:re,onModalOpened:pe}),!F&&!W&&!H&&(0,n.jsxs)("div",{className:"flex flex-col flex-1 min-h-0 relative",children:[(0,n.jsx)(N.B,{isLoading:D,turns:e,settingsMode:F,onReload:()=>{t((e=>{if(e.length<2)return e;const t=e[e.length-1],a=e[e.length-2];return"assistant"===t.role&&"user"===a.role?(s(a.rawContent),e.slice(0,-2)):e})),U(!1),ne("idle")},onEditTurn:(e,a)=>{t((t=>{const s=[...t];return s[e]&&(s[e]={...s[e],rawContent:a}),s}))}}),0===e.length&&!G?.chatMode&&(0,n.jsxs)("div",{className:"fixed bottom-20 left-8 flex flex-col gap-2 z-[5]",children:[(0,n.jsxs)(h.m_,{children:[(0,n.jsx)(h.k$,{asChild:!0,children:(0,n.jsx)(m.$,{"aria-label":"Cycle compute level",variant:"ghost",size:"icon",onClick:()=>{const e=G.computeLevel;V({computeLevel:"low"===e?"medium":"medium"===e?"high":"low"})},className:(0,p.cn)("hover:bg-secondary/70","high"===G.computeLevel?"text-red-600":"medium"===G.computeLevel?"text-orange-300":"text-[var(--text)]"),children:(0,n.jsx)(d.cfR,{})})}),(0,n.jsx)(h.ZI,{side:"right",className:"bg-[var(--active)]/50 text-[var(--text)] border-[var(--text)] max-w-80",children:(0,n.jsx)("p",{children:`Compute Level: ${G.computeLevel?.toUpperCase()}. Click to change. [Warning]: beta feature and resource costly.`})})]}),(0,n.jsxs)(h.m_,{children:[(0,n.jsx)(h.k$,{asChild:!0,children:(0,n.jsx)(m.$,{"aria-label":"Add Web Search Results to LLM Context",variant:"ghost",size:"icon",onClick:()=>{V({chatMode:"web",webMode:G.webMode||_[0].id})},className:"text-[var(--text)] hover:bg-secondary/70",children:(0,n.jsx)(c.pqQ,{})})}),(0,n.jsx)(h.ZI,{side:"right",className:"bg-[var(--active)]/50 text-[var(--text)] border-[var(--text)]",children:(0,n.jsx)("p",{children:"Add Web Search Results to LLM Context"})})]}),(0,n.jsxs)(h.m_,{children:[(0,n.jsx)(h.k$,{asChild:!0,children:(0,n.jsx)(m.$,{"aria-label":"Add Current Web Page to LLM Context",variant:"ghost",size:"icon",onClick:()=>{V({chatMode:"page"})},className:"text-[var(--text)] hover:bg-secondary/70",children:(0,n.jsx)(c.RGv,{})})}),(0,n.jsx)(h.ZI,{side:"right",className:"bg-[var(--active)]/50 text-[var(--text)] border-[var(--text)]",children:(0,n.jsx)("p",{children:"Add Current Web Page to LLM Context"})})]})]}),"page"===G?.chatMode&&(0,n.jsx)("div",{className:(0,p.cn)("fixed bottom-16 left-1/2 -translate-x-1/2","flex flex-row justify-center","w-fit h-8 z-[2]","transition-all duration-200 ease-in-out",X?"opacity-100 translate-y-0":"opacity-0 -translate-y-2.5","bg-transparent px-0 py-0"),style:{backdropFilter:"blur(10px)"},onMouseEnter:()=>ee(!0),onMouseLeave:()=>ee(!1),children:(0,n.jsxs)("div",{className:"flex items-center space-x-6 max-w-full overflow-x-auto px-0",children:[(0,n.jsxs)(h.m_,{children:[(0,n.jsx)(h.k$,{children:(0,n.jsx)(L,{onClick:()=>de("Provide your summary."),children:"TLDR"})}),(0,n.jsx)(h.ZI,{side:"top",className:" text-[var(--text)] border-[var(--text)]/50",children:(0,n.jsx)("p",{children:"Quick Summary"})})]}),(0,n.jsxs)(h.m_,{children:[(0,n.jsx)(h.k$,{children:(0,n.jsx)(L,{onClick:()=>de("Extract all key figures, names, locations, and dates mentioned on this page and list them."),children:"Facts"})}),(0,n.jsx)(h.ZI,{side:"top",className:" text-[var(--text)] border-[var(--text)]/50",children:(0,n.jsx)("p",{children:"Numbers, events, names"})})]}),(0,n.jsxs)(h.m_,{children:[(0,n.jsx)(h.k$,{children:(0,n.jsx)(L,{onClick:()=>de("Find positive developments, achievements, or opportunities mentioned on this page."),children:"Yay!"})}),(0,n.jsx)(h.ZI,{side:"top",className:" text-[var(--text)] border-[var(--text)]/50",children:(0,n.jsx)("p",{children:"Good news"})})]}),(0,n.jsxs)(h.m_,{children:[(0,n.jsx)(h.k$,{children:(0,n.jsx)(L,{onClick:()=>de("Find concerning issues, risks, or criticisms mentioned on this page."),children:"Oops"})}),(0,n.jsx)(h.ZI,{side:"top",className:" text-[var(--text)] border-[var(--text)]/50",children:(0,n.jsx)("p",{children:"Bad news"})})]})]})}),"web"===G?.chatMode&&(0,n.jsx)("div",{className:(0,p.cn)("fixed bottom-14 left-1/2 -translate-x-1/2","flex flex-row justify-center","w-fit h-10 z-[2]","transition-all duration-200 ease-in-out",te?"opacity-100 translate-y-0":"opacity-0 -translate-y-2.5","bg-transparent px-0 py-0"),style:{backdropFilter:"blur(10px)"},onMouseEnter:()=>ae(!0),onMouseLeave:()=>ae(!1),children:(0,n.jsx)("div",{className:"flex items-center space-x-4 max-w-full overflow-x-auto px-4 py-1",children:_.map((e=>(0,n.jsx)(R,{onClick:()=>{V({webMode:e.id,chatMode:"web"})},isActive:G.webMode===e.id,title:e.label,children:(0,n.jsx)(e.icon,{size:18})},e.id)))})})]})]}),!F&&!W&&!H&&(0,n.jsx)("div",{className:"p-2 relative z-[10]",children:(0,n.jsx)(j.p,{isLoading:D,message:a,setMessage:s,onSend:()=>de(a),onStopRequest:ue})}),G?.backgroundImage?(0,n.jsx)(v.V,{}):null,(0,n.jsx)(o.l$,{containerStyle:{borderRadius:16,bottom:"60px"},toastOptions:{duration:2e3,position:"bottom-center",style:{background:"var(--bg)",color:"var(--text)",fontSize:"1rem",border:"1px solid var(--text)",boxShadow:"0 4px 12px rgba(0,0,0,0.1)"},success:{duration:2e3,style:{background:"var(--bg)",color:"var(--text)",fontSize:"1.25rem"}}}})]})})};s()}catch(I){s(I)}}))},9853:(e,t,a)=>{a.d(t,{S:()=>i});var s=a(6540),n=a(6948),r=a(2951);const o=e=>{const t=e.replace(/<think>[\s\S]*?<\/think>/g,"").replace(/"/g,"").replace(/#/g,"").trim();return t&&t.split(/\s+/).slice(0,4).join(" ")||"New Chat"},i=(e,t,a)=>{const[i,l]=(0,s.useState)(""),{config:c}=(0,n.UK)(),d=(0,s.useRef)(null);return(0,s.useEffect)((()=>{if(!e&&t.length>=2&&!i&&c?.generateTitle){d.current&&d.current.abort(),d.current=new AbortController;const e=d.current.signal,a=c?.models?.find((e=>e.id===c.selectedModel));if(!a)return;const s=[...t.slice(0,2).map((e=>({content:e.rawContent||"",role:e.role}))),{role:"user",content:"Create a short 2-4 word title for this chat. Keep it concise, just give me the best one, just one. No explanations or thinking steps needed."}],n=(()=>{const e={body:{model:a.id,messages:s,stream:!["ollama","lmStudio"].includes(a.host||"")},headers:{}};if("ollama"===a.host)return{...e,url:`${c.ollamaUrl}/api/chat`}})();if(!n)return;const i=t=>{e.aborted?console.log("Title generation aborted."):console.error("Title generation failed:",t)};if(["ollama"].includes(a.host||""))fetch(n.url,{method:"POST",headers:{"Content-Type":"application/json",...n.headers},body:JSON.stringify(n.body),signal:e}).then((e=>e.json())).then((e=>{const t=e.choices?.[0]?.message?.content||"",a=o(t);a&&(console.log("Setting chat title (local):",a),l(a))})).catch(i);else{let t="";(0,r.hL)(n.url,n.body,((a,s)=>{if(t=a,e.aborted)console.log("Title streaming aborted during callback.");else if(s){const e=o(t);e&&(console.log("Setting chat title (streaming):",e),l(e))}}),n.headers,a.host||"",e)}}return()=>{d.current&&(d.current.abort(),d.current=null)}}),[e,t,a,c,i]),{chatTitle:i,setChatTitle:l}}}},l={};function c(e){var t=l[e];if(void 0!==t)return t.exports;var a=l[e]={exports:{}};return i[e].call(a.exports,a,a.exports,c),a.exports}c.m=i,e="function"==typeof Symbol?Symbol("webpack queues"):"__webpack_queues__",t="function"==typeof Symbol?Symbol("webpack exports"):"__webpack_exports__",a="function"==typeof Symbol?Symbol("webpack error"):"__webpack_error__",s=e=>{e&&e.d<1&&(e.d=1,e.forEach((e=>e.r--)),e.forEach((e=>e.r--?e.r++:e())))},c.a=(n,r,o)=>{var i;o&&((i=[]).d=-1);var l,c,d,u=new Set,m=n.exports,h=new Promise(((e,t)=>{d=t,c=e}));h[t]=m,h[e]=e=>(i&&e(i),u.forEach(e),h.catch((e=>{}))),n.exports=h,r((n=>{var r;l=(n=>n.map((n=>{if(null!==n&&"object"==typeof n){if(n[e])return n;if(n.then){var r=[];r.d=0,n.then((e=>{o[t]=e,s(r)}),(e=>{o[a]=e,s(r)}));var o={};return o[e]=e=>e(r),o}}var i={};return i[e]=e=>{},i[t]=n,i})))(n);var o=()=>l.map((e=>{if(e[a])throw e[a];return e[t]})),c=new Promise((t=>{(r=()=>t(o)).r=0;var a=e=>e!==i&&!u.has(e)&&(u.add(e),e&&!e.d&&(r.r++,e.push(r)));l.map((t=>t[e](a)))}));return r.r?c:o()}),(e=>(e?d(h[a]=e):c(m),s(i)))),i&&i.d<0&&(i.d=0)},n=[],c.O=(e,t,a,s)=>{if(!t){var r=1/0;for(d=0;d<n.length;d++){for(var[t,a,s]=n[d],o=!0,i=0;i<t.length;i++)(!1&s||r>=s)&&Object.keys(c.O).every((e=>c.O[e](t[i])))?t.splice(i--,1):(o=!1,s<r&&(r=s));if(o){n.splice(d--,1);var l=a();void 0!==l&&(e=l)}}return e}s=s||0;for(var d=n.length;d>0&&n[d-1][2]>s;d--)n[d]=n[d-1];n[d]=[t,a,s]},c.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return c.d(t,{a:t}),t},o=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,c.t=function(e,t){if(1&t&&(e=this(e)),8&t)return e;if("object"==typeof e&&e){if(4&t&&e.__esModule)return e;if(16&t&&"function"==typeof e.then)return e}var a=Object.create(null);c.r(a);var s={};r=r||[null,o({}),o([]),o(o)];for(var n=2&t&&e;"object"==typeof n&&!~r.indexOf(n);n=o(n))Object.getOwnPropertyNames(n).forEach((t=>s[t]=()=>e[t]));return s.default=()=>e,c.d(a,s),a},c.d=(e,t)=>{for(var a in t)c.o(t,a)&&!c.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})},c.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),c.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),c.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},c.j=524,(()=>{var e={524:0};c.O.j=t=>0===e[t];var t=(t,a)=>{var s,n,[r,o,i]=a,l=0;if(r.some((t=>0!==e[t]))){for(s in o)c.o(o,s)&&(c.m[s]=o[s]);if(i)var d=i(c)}for(t&&t(a);l<r.length;l++)n=r[l],c.o(e,n)&&e[n]&&e[n][0](),e[n]=0;return c.O(d)},a=self.webpackChunkchromepanion=self.webpackChunkchromepanion||[];a.forEach(t.bind(null,0)),a.push=t.bind(null,a.push.bind(a))})(),c.nc=void 0;var d=c.O(void 0,[465],(()=>c(3003)));d=c.O(d)})();